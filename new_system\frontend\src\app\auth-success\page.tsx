'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AuthSuccessPage() {
  const router = useRouter();

  useEffect(() => {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const userData = urlParams.get('user');
    
    if (token && userData) {
      try {
        // Store authentication data
        localStorage.setItem('token', token);
        localStorage.setItem('user', userData);
        localStorage.setItem('isAuthenticated', 'true');
        
        console.log('Authentication data stored successfully');
        
        // Redirect to dashboard
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 1000);
        
      } catch (error) {
        console.error('Error storing authentication data:', error);
        window.location.href = '/login';
      }
    } else {
      // No authentication data, redirect to login
      window.location.href = '/login';
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Login Successful!</h1>
        <p className="text-gray-600 mb-4">Redirecting to dashboard...</p>
        <div className="text-sm text-gray-500">
          If you are not redirected automatically, 
          <a href="/dashboard" className="text-blue-600 hover:underline ml-1">
            click here
          </a>
        </div>
      </div>
    </div>
  );
}
