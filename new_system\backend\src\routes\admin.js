const express = require('express');
const { body, validationResult } = require('express-validator');
const { adminOnly } = require('../middleware/roleMiddleware');
const { authMiddleware } = require('../middleware/auth');

// Import admin controllers
const AdminDashboardController = require('../controllers/admin/AdminDashboardController');
const AdminLoadController = require('../controllers/admin/AdminLoadController');
const AdminEnterpriseController = require('../controllers/admin/AdminEnterpriseController');
const AdminUserController = require('../controllers/admin/AdminUserController');
const AdminVehicleController = require('../controllers/admin/AdminVehicleController');
const AdminDriverController = require('../controllers/admin/AdminDriverController');
const AdminMaterialController = require('../controllers/admin/AdminMaterialController');
const AdminTruckTypeController = require('../controllers/admin/AdminTruckTypeController');
const AdminReportController = require('../controllers/admin/AdminReportController');
const AdminBankGurantyController = require('../controllers/admin/AdminBankGurantyController');
const AdminSupervisorController = require('../controllers/admin/AdminSupervisorController');
const AdminSailPushLoadController = require('../controllers/admin/AdminSailPushLoadController');
const SmsTrackController = require('../controllers/admin/SmsTrackController');

const router = express.Router();

// Apply authentication and admin-only middleware to all admin routes
router.use(authMiddleware);
router.use(adminOnly);

// ============================================================================
// DASHBOARD & ANALYTICS ROUTES
// ============================================================================

// @route   GET /api/admin/dashboard
// @desc    Admin dashboard with statistics - Matches bidding_system exactly
// @access  Admin
router.get('/dashboard', AdminDashboardController.showDashBoardList);

// @route   GET /api/admin/dashboardAllLoadCount
// @desc    Get dashboard load count statistics - Matches bidding_system exactly
// @access  Admin
router.get('/dashboardAllLoadCount', AdminDashboardController.showDashBoardAllLoadCount);

// @route   ANY /api/admin/dashboardLoadCountListDetails
// @desc    Get detailed load count information - Matches bidding_system exactly
// @access  Admin
router.post('/dashboardLoadCountListDetails', AdminDashboardController.loadCountListDetails);

// @route   POST /api/admin/dashboard/load-details
// @desc    Get detailed load count information
// @access  Admin
router.post('/dashboard/load-details', AdminDashboardController.loadCountListDetails);

// @route   POST /api/admin/dashboard/load-analysis
// @desc    Get load analysis chart data
// @access  Admin
router.post('/dashboard/load-analysis', AdminDashboardController.loadAnalysisChart);

// @route   POST /api/admin/dashboard/bid-analysis
// @desc    Get bid analysis data
// @access  Admin
router.post('/dashboard/bid-analysis', AdminDashboardController.bidAnalysisData);

// @route   POST /api/admin/dashboard/transporter-participation
// @desc    Get transporter participation data
// @access  Admin
router.post('/dashboard/transporter-participation', AdminDashboardController.transpoterParticipation);

// @route   POST /api/admin/dashboard/transporter-analysis
// @desc    Get transporter analysis chart data
// @access  Admin
router.post('/dashboard/transporter-analysis', AdminDashboardController.transpoterAnalysisChart);

// @route   POST /api/admin/dashboard/performance-analysis
// @desc    Get performance analysis chart data
// @access  Admin
router.post('/dashboard/performance-analysis', AdminDashboardController.performanceAnalysisChart);

// Branch and shipper search routes - Match bidding_system exactly
router.post('/shipper_branch_search_admin', AdminDashboardController.shipperWiseBranchSearch);
router.post('/shipperId_branch_search_admin', AdminDashboardController.shipperIdWiseBranchSearch);

// Dashboard analysis routes - Match bidding_system exactly (support both GET and POST)
router.get('/dashboardLoadAnalysisChart', AdminDashboardController.loadAnalysisChart);
router.post('/dashboardLoadAnalysisChart', AdminDashboardController.loadAnalysisChart);
router.get('/dashboardBidAnalysis', AdminDashboardController.bidAnalysisData);
router.post('/dashboardBidAnalysis', AdminDashboardController.bidAnalysisData);
router.get('/dashboardTranspoterParticipation', AdminDashboardController.transpoterParticipation);
router.post('/dashboardTranspoterParticipation', AdminDashboardController.transpoterParticipation);
router.get('/dashboardPerformanceAnalysisChart', AdminDashboardController.performanceAnalysisChart);
router.post('/dashboardPerformanceAnalysisChart', AdminDashboardController.performanceAnalysisChart);

// History filter routes - Match bidding_system exactly
router.post('/cancel_history_filter_admin', AdminDashboardController.cancelationHistoryFilter);
router.post('/baseprice_history_filter_admin', AdminDashboardController.basepriceHistoryFilter);

// ============================================================================
// LOAD MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/loads
// @desc    Get load list
// @access  Admin
router.get('/loads', AdminLoadController.getLoadList);

// @route   GET /api/admin/loads/create
// @desc    Get create load page data
// @access  Admin
router.get('/loads/create', AdminLoadController.getCreatLoadPage);

// @route   GET /api/admin/create-load-data
// @desc    Get create load form data - Matches bidding_system exactly
// @access  Admin
router.get('/create-load-data', AdminLoadController.getCreatLoadPage);

// @route   POST /api/admin/create-load
// @desc    Create new load - Matches bidding_system exactly
// @access  Admin
router.post('/create-load', AdminLoadController.postCreateLoad);

// @route   POST /api/admin/get_consignee_phone
// @desc    Get consignee phone number - Matches bidding_system exactly
// @access  Admin
router.post('/get_consignee_phone', AdminLoadController.getConsigneePhone);

// @route   POST /api/admin/shiper_association_List
// @desc    Get shipper association list - Matches bidding_system exactly
// @access  Admin
router.post('/shiper_association_List', AdminLoadController.getConsigneeAssociationList);

// @route   POST /api/admin/get-branch-list-page
// @desc    Get branch list for selected consignee - Matches bidding_system exactly
// @access  Admin
router.post('/get-branch-list-page', AdminLoadController.getBranchListPage);

// @route   POST /api/admin/loads/create
// @desc    Create new load
// @access  Admin
router.post('/loads/create', [
  body('consigneeId').notEmpty().withMessage('Consignee is required'),
  body('loadDate').isISO8601().withMessage('Valid load date is required'),
  body('basePrice').isNumeric().withMessage('Base price must be numeric'),
  body('pickupLocationAddress').notEmpty().withMessage('Pickup location is required'),
  body('dropLocationAddress').notEmpty().withMessage('Drop location is required')
], AdminLoadController.postCreateLoad);

// @route   GET /api/admin/loads/:id/edit
// @desc    Get edit load page data
// @access  Admin
router.get('/loads/:id/edit', AdminLoadController.getEditLoadPage);

// @route   POST /api/admin/loads/:id/edit
// @desc    Update existing load
// @access  Admin
router.post('/loads/:id/edit', AdminLoadController.postUpdateLoad);

// @route   GET /api/admin/loads/:id/details
// @desc    View load details
// @access  Admin
router.get('/loads/:id/details', AdminLoadController.viewLoadDetails);

// @route   POST /api/admin/loads/:id/select-quote
// @desc    Select quotation for load
// @access  Admin
router.post('/loads/:id/select-quote', AdminLoadController.adminSelectQuote);

// @route   POST /api/admin/loads/:id/cancel
// @desc    Cancel load
// @access  Admin
router.post('/loads/:id/cancel', AdminLoadController.adminCancelLoad);

// @route   POST /api/admin/loads/:id/start-trip
// @desc    Start trip for load
// @access  Admin
router.post('/loads/:id/start-trip', AdminLoadController.startTrip);

// Load helper routes
router.get('/truck-types', AdminLoadController.gettruckType);
router.get('/material-types', AdminLoadController.getmaterialType);
router.get('/consignees', AdminLoadController.getconsignee);
router.get('/fleet-list', AdminLoadController.getfleetList);
router.post('/check-contractual', AdminLoadController.checkContaractual);
router.post('/loading-point', AdminLoadController.getLoadingPoint);
router.post('/truck-weight', AdminLoadController.truckweigt);
router.post('/branch-list', AdminLoadController.getBranchListPage);
router.post('/bidding-type', AdminLoadController.getBiddingType);
router.post('/fixed-float-comment', AdminLoadController.getFixedFloatComment);
router.post('/shipper-association-list', AdminLoadController.getConsigneeAssociationList);
router.post('/consignee-phone', AdminLoadController.getConsigneePhone);

// ============================================================================
// ENTERPRISE MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/enterprises
// @desc    Get enterprise list
// @access  Admin
router.get('/enterprises', AdminEnterpriseController.showEnterpriseList);

// @route   GET /api/admin/enterprises/create
// @desc    Get add enterprise page data
// @access  Admin
router.get('/enterprises/create', AdminEnterpriseController.getAddEnterpriseList);

// @route   POST /api/admin/enterprises/create
// @desc    Create new enterprise
// @access  Admin
router.post('/enterprises/create', [
  body('organisationName').notEmpty().withMessage('Organisation name is required'),
  body('contactPersonName').notEmpty().withMessage('Contact person name is required'),
  body('contactPersonMobile').isMobilePhone().withMessage('Valid mobile number is required'),
  body('type').isIn(['CONSIGNEE', 'FLEET']).withMessage('Valid enterprise type is required')
], AdminEnterpriseController.postAddEnterpriseList);

// @route   POST /api/admin/enterprises/new-registration
// @desc    Create new registration enterprise
// @access  Admin
router.post('/enterprises/new-registration', AdminEnterpriseController.postAddNewRegistrationEnterprise);

// @route   GET /api/admin/enterprises/:id
// @desc    Get enterprise details
// @access  Admin
router.get('/enterprises/:id', AdminEnterpriseController.getEnterpriseDetails);

// @route   POST /api/admin/enterprises/:id/verify
// @desc    Verify enterprise
// @access  Admin
router.post('/enterprises/:id/verify', AdminEnterpriseController.EnterpriseVerify);

// @route   GET /api/admin/enterprises/:id/edit
// @desc    Get edit enterprise page data
// @access  Admin
router.get('/enterprises/:id/edit', AdminEnterpriseController.getEditEnterprise);

// @route   POST /api/admin/enterprises/:id/update
// @desc    Update enterprise
// @access  Admin
router.post('/enterprises/:id/update', AdminEnterpriseController.postUpdateEnterprise);

// @route   POST /api/admin/enterprises/submit-update
// @desc    Submit enterprise update
// @access  Admin
router.post('/enterprises/submit-update', AdminEnterpriseController.submitupdateEnterprise);

// ============================================================================
// USER MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/users
// @desc    Get user list
// @access  Admin
router.get('/users', AdminUserController.showUserList);

// @route   GET /api/admin/users/create
// @desc    Get add user page data
// @access  Admin
router.get('/users/create', AdminUserController.getAddUser);

// @route   POST /api/admin/users/create
// @desc    Create new user
// @access  Admin
router.post('/users/create', [
  body('name').notEmpty().withMessage('Name is required'),
  body('mobile').isMobilePhone().withMessage('Valid mobile number is required'),
  body('userType').isIn(['ADMIN', 'CONSIGNEE', 'FLEET']).withMessage('Valid user type is required')
], AdminUserController.postAddUser);

// @route   GET /api/admin/users/:id/edit
// @desc    Get edit user page data
// @access  Admin
router.get('/users/:id/edit', AdminUserController.geteditUser);

// @route   POST /api/admin/users/:id/update
// @desc    Update user
// @access  Admin
router.post('/users/:id/update', AdminUserController.postupdateUser);

// @route   POST /api/admin/users/:id/deactivate
// @desc    Deactivate user
// @access  Admin
router.post('/users/:id/deactivate', AdminUserController.postdeactiveUser);

// @route   POST /api/admin/users/:id/activate
// @desc    Activate user
// @access  Admin
router.post('/users/:id/activate', AdminUserController.postactiveUser);

// ============================================================================
// VEHICLE MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/vehicles
// @desc    Get vehicle list
// @access  Admin
router.get('/vehicles', AdminVehicleController.showVehicleList);

// @route   GET /api/admin/vehicles/create
// @desc    Get add vehicle page data
// @access  Admin
router.get('/vehicles/create', AdminVehicleController.addVehicle);

// @route   POST /api/admin/vehicles/create
// @desc    Create new vehicle
// @access  Admin
router.post('/vehicles/create', [
  body('vehicleNumber').notEmpty().withMessage('Vehicle number is required'),
  body('truckTypeId').isInt().withMessage('Truck type is required'),
  body('fleetId').isInt().withMessage('Fleet is required')
], AdminVehicleController.postAddVehicle);

// @route   GET /api/admin/vehicles/:id
// @desc    Get vehicle details
// @access  Admin
router.get('/vehicles/:id', AdminVehicleController.getVehicleDetails);

// @route   POST /api/admin/vehicles/:id/deactivate
// @desc    Deactivate vehicle
// @access  Admin
router.post('/vehicles/:id/deactivate', AdminVehicleController.postVehicledeactive);

// @route   POST /api/admin/vehicles/:id/approve
// @desc    Approve vehicle
// @access  Admin
router.post('/vehicles/:id/approve', AdminVehicleController.postVehicleApprove);

// ============================================================================
// DRIVER MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/drivers
// @desc    Get driver list
// @access  Admin
router.get('/drivers', AdminDriverController.showDriverList);

// @route   POST /api/admin/drivers/:id/deactivate
// @desc    Deactivate driver
// @access  Admin
router.post('/drivers/:id/deactivate', AdminDriverController.postDriverReject);

// @route   POST /api/admin/drivers/:id/approve
// @desc    Approve driver
// @access  Admin
router.post('/drivers/:id/approve', AdminDriverController.postDriverApprove);

// ============================================================================
// MATERIAL & TRUCK TYPE MANAGEMENT ROUTES
// ============================================================================

// Material Management
router.get('/materials', AdminMaterialController.showMaterialList);
router.get('/materials/create', AdminMaterialController.getAddMaterial);
router.post('/materials/create', [
  body('name').notEmpty().withMessage('Material name is required')
], AdminMaterialController.postAddMaterial);
router.get('/materials/:id/edit', AdminMaterialController.getEditMaterial);
router.post('/materials/:id/update', AdminMaterialController.postUpdateMaterial);

// Truck Type Management
router.get('/truck-types-admin', AdminTruckTypeController.showTruckTypeList);
router.get('/truck-types-admin/create', AdminTruckTypeController.getAddTruckType);
router.post('/truck-types-admin/create', [
  body('name').notEmpty().withMessage('Truck type name is required'),
  body('capacity').isNumeric().withMessage('Capacity must be numeric')
], AdminTruckTypeController.postAddTruckType);

// ============================================================================
// REPORTS ROUTES
// ============================================================================

router.get('/reports', AdminReportController.showReportList);
router.get('/reports/download', AdminReportController.getAdminAllReportDownload);
router.post('/reports/download-csv', AdminReportController.getAdminAllReportDownloadFile);
router.post('/reports/branch-list-by-org', AdminReportController.getAdminAllBranchListByOrgId);
router.get('/reports/rate-trend', AdminReportController.getAdminAllReportRateTrend);
router.post('/reports/rate-trend-html', AdminReportController.getAdminAllReportRateTrendHtml);
router.post('/reports/rate-trend-download', AdminReportController.getAdminAllReportRateTrendDownload);

// ============================================================================
// TRACKING & SMS ROUTES
// ============================================================================

router.get('/sms-list', AdminUserController.showSmsList);
router.get('/manual-tracking', AdminUserController.showManualTracking);
router.get('/manual-tracking/data', AdminUserController.getManualTracking);
router.get('/track-status-change/:loadId/:key/:shipper/:supplier/:reached', AdminUserController.trackStatusChanges);
router.get('/sms/create', AdminUserController.getAddSms);
router.get('/tracking/create', AdminUserController.getAddTracking);
router.post('/tracking/add-comment', AdminUserController.AddAnComment);
router.post('/tracking/add-party', AdminUserController.AddParty);
router.get('/sms/:id/edit', AdminUserController.getEditSms);
router.post('/tracking/search', AdminUserController.SearchForNewLoad);
router.get('/tracking/:id/edit', AdminUserController.getEditTracking);
router.post('/sms/create', AdminUserController.postAddSms);
router.post('/sms/update', AdminUserController.postUpdateSms);
router.get('/sms-provider/:id/activate', AdminUserController.postActiveSms);

// ============================================================================
// BANK GUARANTY ROUTES
// ============================================================================

router.get('/bank-guaranty', AdminBankGurantyController.showBankGurantyList);
router.get('/bank-guaranty/create', AdminBankGurantyController.getAddBankGurantyList);
router.get('/bank-guaranty/:id/edit', AdminBankGurantyController.geteditBankGuranty);
router.post('/bank-guaranty/save', AdminBankGurantyController.postAddBankGurantyList);
router.post('/bank-guaranty/trans-list', AdminBankGurantyController.getTransListPage);
router.post('/bank-guaranty/update', AdminBankGurantyController.postUpdateBankGurantyList);

// ============================================================================
// SUPERVISOR MANAGEMENT ROUTES
// ============================================================================

router.get('/supervisors', AdminSupervisorController.getLoadSupervisorList);
router.get('/supervisors/forward-list', AdminSupervisorController.forwardToSupervisorList);
router.get('/supervisors/branch-loads/:enterpriseId', AdminSupervisorController.getBranchLoadsFromEnterprise);
router.get('/supervisors/:enterpriseId/:branchId', AdminSupervisorController.getLoadSupervisorListWithEnterpriseId);
router.get('/supervisors/create/:enterpriseId', AdminSupervisorController.getLoadSupervisorAdd);
router.get('/supervisors/:id/edit', AdminSupervisorController.getLoadSupervisorEdit);
router.post('/supervisors/save', AdminSupervisorController.postLoadSupervisorSave);
router.post('/supervisors/update', AdminSupervisorController.postLoadSupervisorUpdate);

// ============================================================================
// SAIL INTEGRATION ROUTES
// ============================================================================

router.get('/sail/push-load', AdminSailPushLoadController.index);
router.post('/sail/push-load', AdminSailPushLoadController.pushLoad);

// ============================================================================
// SMS TRACKING ROUTES
// ============================================================================

router.get('/sms-track', SmsTrackController.index);
router.post('/sms-track/data', SmsTrackController.data);
router.post('/sms-track/csv', SmsTrackController.csv);
router.post('/sms-track/email', SmsTrackController.email);

module.exports = router;
