"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/create_load/page",{

/***/ "(app-pages-browser)/./src/app/admin/create_load/page.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/create_load/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminCreateLoadPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyRupeeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CalendarIcon,CheckCircleIcon,CurrencyRupeeIcon,DocumentTextIcon,MapPinIcon,PlusIcon,TruckIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Enhanced universal admin create load component matching bidding_system exactly\nfunction AdminCreateLoad() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        consigneeId: \"\",\n        branchId: \"\",\n        biddingType: \"\",\n        loadType: \"public\",\n        pickupAddress: \"\",\n        pickupLat: \"\",\n        pickupLon: \"\",\n        pickupName: \"\",\n        pickupMobile: \"\",\n        dropAddress: \"\",\n        dropLat: \"\",\n        dropLon: \"\",\n        dropName: \"\",\n        dropMobile: \"\",\n        truckTypeId: \"\",\n        materialType: \"\",\n        weight: \"\",\n        loadPrice: \"\",\n        basePrice: \"\",\n        loadDateTime: \"\",\n        loadDateTimeTo: \"\",\n        bidDateTime: \"\",\n        comments: \"\",\n        packaging: \"\",\n        numberOfVehicles: \"1\",\n        groupIds: [],\n        fleetId: \"\",\n        fleetownerPrice: \"\"\n    });\n    const [masterData, setMasterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        consignees: [],\n        branches: [],\n        truckTypes: [],\n        materialTypes: [],\n        fleetList: [],\n        associations: []\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showPrivateGroup, setShowPrivateGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showIndentSection, setShowIndentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadUnloadLocations, setLoadUnloadLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            address: \"\",\n            type: \"\",\n            latitude: \"\",\n            longitude: \"\"\n        }\n    ]);\n    // Fetch master data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMasterData = async ()=>{\n            try {\n                const response = await fetch(\"/api/admin/create-load-data\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\")),\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    setMasterData(data.data);\n                }\n            } catch (err) {\n                console.error(\"Failed to fetch master data:\", err);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchMasterData();\n    }, []);\n    // Handle consignee selection and fetch branches - matches bidding_system exactly\n    const handleConsigneeChange = async (consigneeId)=>{\n        setFormData((prev)=>({\n                ...prev,\n                consigneeId,\n                branchId: \"\"\n            })); // Reset branch when shipper changes\n        try {\n            // Fetch branches for the selected consignee - matches bidding_system getBranchListPage\n            const branchResponse = await fetch(\"/api/admin/get-branch-list-page\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\")),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    id: consigneeId\n                })\n            });\n            if (branchResponse.ok) {\n                const branchData = await branchResponse.json();\n                if (branchData.status === 1) {\n                    setMasterData((prev)=>({\n                            ...prev,\n                            branches: branchData.data\n                        }));\n                } else {\n                    setMasterData((prev)=>({\n                            ...prev,\n                            branches: []\n                        }));\n                }\n            }\n            // Fetch consignee phone\n            const phoneResponse = await fetch(\"/api/admin/get_consignee_phone\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\")),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    consigneeId\n                })\n            });\n            if (phoneResponse.ok) {\n                const phoneData = await phoneResponse.json();\n                const consignee = masterData.consignees.find((c)=>c.consignee_id === parseInt(consigneeId));\n                const consigneeName = (consignee === null || consignee === void 0 ? void 0 : consignee.consignee_name) || \"\";\n                const organisationName = (consignee === null || consignee === void 0 ? void 0 : consignee.organisation_name) || \"\";\n                setFormData((prev)=>({\n                        ...prev,\n                        pickupName: consigneeName,\n                        dropName: consigneeName,\n                        pickupMobile: phoneData.consignee_phone || \"\",\n                        dropMobile: phoneData.consignee_phone || \"\"\n                    }));\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch consignee data:\", err);\n            setMasterData((prev)=>({\n                    ...prev,\n                    branches: []\n                }));\n        }\n    };\n    // Handle load type change\n    const handleLoadTypeChange = async (loadType)=>{\n        setFormData((prev)=>({\n                ...prev,\n                loadType\n            }));\n        setShowPrivateGroup(false);\n        setShowIndentSection(false);\n        if (loadType === \"private\") {\n            setShowPrivateGroup(true);\n            // Fetch associations for the selected consignee\n            if (formData.consigneeId) {\n                try {\n                    const response = await fetch(\"/api/admin/shiper_association_List\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\")),\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            consignee_id: formData.consigneeId\n                        })\n                    });\n                    if (response.ok) {\n                        const associations = await response.json();\n                        setMasterData((prev)=>({\n                                ...prev,\n                                associations\n                            }));\n                    }\n                } catch (err) {\n                    console.error(\"Failed to fetch associations:\", err);\n                }\n            }\n        } else if (loadType === \"indent\") {\n            setShowIndentSection(true);\n        }\n    };\n    // Add more load/unload locations\n    const addLoadUnloadLocation = ()=>{\n        setLoadUnloadLocations((prev)=>[\n                ...prev,\n                {\n                    address: \"\",\n                    type: \"\",\n                    latitude: \"\",\n                    longitude: \"\"\n                }\n            ]);\n    };\n    // Remove load/unload location\n    const removeLoadUnloadLocation = (index)=>{\n        setLoadUnloadLocations((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            // Validate form data\n            const newErrors = {};\n            if (!formData.consigneeId) newErrors.consigneeId = \"Please select a shipper\";\n            if (!formData.pickupAddress) newErrors.pickupAddress = \"Please enter pickup location\";\n            if (!formData.dropAddress) newErrors.dropAddress = \"Please enter drop location\";\n            if (!formData.truckTypeId) newErrors.truckTypeId = \"Please select truck type\";\n            if (!formData.weight) newErrors.weight = \"Please enter weight\";\n            if (formData.loadType === \"private\" && (!formData.groupIds || formData.groupIds.length === 0)) {\n                newErrors.groupIds = \"Please select an association\";\n            }\n            if (formData.loadType === \"indent\") {\n                if (!formData.fleetId) newErrors.fleetId = \"Please select a supply partner\";\n                if (!formData.fleetownerPrice) newErrors.fleetownerPrice = \"Please enter supplier price\";\n            }\n            if (Object.keys(newErrors).length > 0) {\n                setErrors(newErrors);\n                setIsLoading(false);\n                return;\n            }\n            // Submit form data\n            const response = await fetch(\"/api/admin/create-load\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\")),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    loadUnloadLocations\n                })\n            });\n            if (response.ok) {\n                const result = await response.json();\n                // Show success message and redirect\n                alert(\"Load created successfully!\");\n                window.location.href = \"/admin/load_list\";\n            } else {\n                const error = await response.json();\n                alert(error.message || \"Failed to create load\");\n            }\n        } catch (err) {\n            console.error(\"Failed to create load:\", err);\n            alert(\"Failed to create load. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-b-4 border-[#0e2d67] mx-auto mb-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Loading Create Load Form\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Fetching form data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: \"Create Load\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Create a new load for bidding in the transport marketplace\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"h-6 w-6 text-[#0e2d67] mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Shipper Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: [\n                                                                    \"Select Shipper \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.consigneeId,\n                                                                onChange: (e)=>handleConsigneeChange(e.target.value),\n                                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(errors.consigneeId ? \"border-red-500\" : \"border-gray-300\"),\n                                                                required: true,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Choose a shipper\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    masterData.consignees.map((consignee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: consignee.consignee_id,\n                                                                            children: [\n                                                                                consignee.consignee_name,\n                                                                                \" - \",\n                                                                                consignee.organisation_name,\n                                                                                consignee.is_verified && \" ✓\"\n                                                                            ]\n                                                                        }, consignee.consignee_id, true, {\n                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.consigneeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: errors.consigneeId\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: [\n                                                                    \"Select Branch \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 37\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.branchId,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            branchId: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(!formData.consigneeId ? \"bg-gray-100 cursor-not-allowed\" : \"border-gray-300\"),\n                                                                disabled: !formData.consigneeId,\n                                                                required: true,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: !formData.consigneeId ? \"Select Shipper First\" : \"Select Branch\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    masterData.branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: branch.id,\n                                                                            children: [\n                                                                                branch.name,\n                                                                                \" (\",\n                                                                                branch.city,\n                                                                                \", \",\n                                                                                branch.state,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, branch.id, true, {\n                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            !formData.consigneeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-sm mt-1\",\n                                                                children: \"Please select a shipper first to load branches\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: [\n                                                                    \"Bidding Type \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.biddingType,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            biddingType: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                                required: true,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select Bidding Type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"0\",\n                                                                        children: \"Spot\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"1\",\n                                                                        children: \"Contractual\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-6 w-6 text-[#e3000f] mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Load Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-3 gap-4\",\n                                                    children: [\n                                                        \"public\",\n                                                        \"private\",\n                                                        \"indent\"\n                                                    ].map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center space-x-2 cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"radio\",\n                                                                    name: \"loadType\",\n                                                                    value: type,\n                                                                    checked: formData.loadType === type,\n                                                                    onChange: (e)=>handleLoadTypeChange(e.target.value),\n                                                                    className: \"text-[#0e2d67] focus:ring-[#0e2d67]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700 capitalize\",\n                                                                    children: type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, type, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        showPrivateGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Select Association\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Choose Associations \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 39\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            multiple: true,\n                                            value: formData.groupIds,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        groupIds: Array.from(e.target.selectedOptions, (option)=>option.value)\n                                                    })),\n                                            className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent min-h-[120px] \".concat(errors.groupIds ? \"border-red-500\" : \"border-gray-300\"),\n                                            children: masterData.associations.map((association)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: association.id,\n                                                    children: [\n                                                        association.association_name,\n                                                        \" (Partners: \",\n                                                        association.number_of_fleet,\n                                                        \")\"\n                                                    ]\n                                                }, association.id, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.groupIds && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm mt-1\",\n                                            children: errors.groupIds\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm mt-1\",\n                                            children: \"Hold Ctrl/Cmd to select multiple associations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, this),\n                        showIndentSection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Supply Partner\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Select Supply Partner \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.fleetId,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                fleetId: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(errors.fleetId ? \"border-red-500\" : \"border-gray-300\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Choose a Supply Partner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        masterData.fleetList.map((fleet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: fleet.id,\n                                                                children: [\n                                                                    fleet.organisationName,\n                                                                    fleet.isVerified && \" ✓\"\n                                                                ]\n                                                            }, fleet.id, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.fleetId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: errors.fleetId\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Supplier Price \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: formData.fleetownerPrice,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        fleetownerPrice: e.target.value\n                                                                    })),\n                                                            className: \"w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(errors.fleetownerPrice ? \"border-red-500\" : \"border-gray-300\"),\n                                                            placeholder: \"Enter supplier price\",\n                                                            min: \"0\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.fleetownerPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: errors.fleetownerPrice\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#0e2d67] mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Location Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-medium text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Pickup Location\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: [\n                                                                \"Pickup Address \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 36\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.pickupAddress,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        pickupAddress: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(errors.pickupAddress ? \"border-red-500\" : \"border-gray-300\"),\n                                                            placeholder: \"Enter pickup location\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.pickupAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.pickupAddress\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Contact Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: formData.pickupName,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                pickupName: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                                    placeholder: \"Contact person name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Contact Mobile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"tel\",\n                                                                    value: formData.pickupMobile,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                pickupMobile: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                                    placeholder: \"Mobile number\",\n                                                                    pattern: \"[6789][0-9]{9}\",\n                                                                    maxLength: \"10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-medium text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-red-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Drop Location\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: [\n                                                                \"Drop Address \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 34\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.dropAddress,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        dropAddress: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(errors.dropAddress ? \"border-red-500\" : \"border-gray-300\"),\n                                                            placeholder: \"Enter drop location\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.dropAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-sm mt-1\",\n                                                            children: errors.dropAddress\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Contact Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: formData.dropName,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                dropName: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                                    placeholder: \"Contact person name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Contact Mobile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"tel\",\n                                                                    value: formData.dropMobile,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                dropMobile: e.target.value\n                                                                            })),\n                                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                                    placeholder: \"Mobile number\",\n                                                                    pattern: \"[6789][0-9]{9}\",\n                                                                    maxLength: \"10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                    children: \"Additional Load/Unload Locations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addLoadUnloadLocation,\n                                            className: \"inline-flex items-center px-4 py-2 bg-[#0e2d67] text-white rounded-lg hover:bg-[#0e2d67]/90 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add More\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: loadUnloadLocations.map((location, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-gray-200 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: location.address,\n                                                        onChange: (e)=>{\n                                                            const newLocations = [\n                                                                ...loadUnloadLocations\n                                                            ];\n                                                            newLocations[index].address = e.target.value;\n                                                            setLoadUnloadLocations(newLocations);\n                                                        },\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                        placeholder: \"Enter location\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: location.type,\n                                                        onChange: (e)=>{\n                                                            const newLocations = [\n                                                                ...loadUnloadLocations\n                                                            ];\n                                                            newLocations[index].type = e.target.value;\n                                                            setLoadUnloadLocations(newLocations);\n                                                        },\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"L\",\n                                                                children: \"Load\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"U\",\n                                                                children: \"Unload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeLoadUnloadLocation(index),\n                                                        className: \"inline-flex items-center px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Remove\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#e3000f] mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Load Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Truck Type \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 30\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.truckTypeId,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                truckTypeId: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(errors.truckTypeId ? \"border-red-500\" : \"border-gray-300\"),\n                                                    required: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select Truck Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        masterData.truckTypes.map((truck)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: truck.id,\n                                                                children: [\n                                                                    truck.name,\n                                                                    \" (\",\n                                                                    truck.capacity,\n                                                                    \")\"\n                                                                ]\n                                                            }, truck.id, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.truckTypeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: errors.truckTypeId\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Material Type \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.materialType,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                materialType: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                    required: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select Material Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        masterData.materialTypes.map((material)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: material.id,\n                                                                children: material.name\n                                                            }, material.id, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Weight (in tons) \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: formData.weight,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                weight: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent \".concat(errors.weight ? \"border-red-500\" : \"border-gray-300\"),\n                                                    placeholder: \"Enter weight\",\n                                                    min: \"0\",\n                                                    step: \"0.01\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.weight && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: errors.weight\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Number of Vehicles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: formData.numberOfVehicles,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                numberOfVehicles: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                    placeholder: \"Number of vehicles\",\n                                                    min: \"1\",\n                                                    defaultValue: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Packaging\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.packaging,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                packaging: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                    placeholder: \"Packaging details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Unloading Weight\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: formData.unloadingWeight,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                unloadingWeight: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                    placeholder: \"Unloading weight\",\n                                                    min: \"0\",\n                                                    step: \"0.01\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Pricing Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Load Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: formData.loadPrice,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        loadPrice: e.target.value\n                                                                    })),\n                                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                            placeholder: \"Enter load price\",\n                                                            min: \"0\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Base Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: formData.basePrice,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        basePrice: e.target.value\n                                                                    })),\n                                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                            placeholder: \"Enter base price\",\n                                                            min: \"0\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 782,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Schedule Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Load Date & Time \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 826,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"datetime-local\",\n                                                    value: formData.loadDateTime,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                loadDateTime: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 829,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Load Date To\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"datetime-local\",\n                                                    value: formData.loadDateTimeTo,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                loadDateTimeTo: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.loadType !== \"indent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Bid Date & Time \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"datetime-local\",\n                                                    value: formData.bidDateTime,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                bidDateTime: e.target.value\n                                                            })),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 824,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-gray-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Additional Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Comments\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.comments,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        comments: e.target.value\n                                                    })),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                            rows: \"4\",\n                                            placeholder: \"Enter any additional comments or instructions...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>window.history.back(),\n                                    className: \"px-8 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"px-8 py-3 bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Creating Load...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CalendarIcon_CheckCircleIcon_CurrencyRupeeIcon_DocumentTextIcon_MapPinIcon_PlusIcon_TruckIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Create Load\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                            lineNumber: 885,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminCreateLoad, \"1mV90IZA0JYPeCr+Zhpgx2l4M08=\");\n_c = AdminCreateLoad;\nfunction AdminCreateLoadPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminCreateLoad, {}, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\create_load\\\\page.tsx\",\n        lineNumber: 918,\n        columnNumber: 10\n    }, this);\n}\n_c1 = AdminCreateLoadPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AdminCreateLoad\");\n$RefreshReg$(_c1, \"AdminCreateLoadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/create_load/page.tsx\n"));

/***/ })

});