const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const logger = require('../../utils/logger');

const prisma = new PrismaClient();

class AuthController {
  // Login page - matches bidding_system /bypass/login
  static async index(req, res) {
    try {
      res.json({
        success: true,
        message: 'Login page',
        data: {
          title: 'ePari<PERSON>han Login',
          loginTypes: ['mobile']
        }
      });
    } catch (error) {
      logger.error('Login page error:', error);
      res.status(500).json({
        success: false,
        error: 'Server error'
      });
    }
  }

  // Mobile-based login - matches bidding_system /bypass/get-otp
  static async login(req, res) {
    try {
      const { login_mobile } = req.body;

      // Validate mobile number - matches bidding_system validation
      if (!login_mobile) {
        return res.status(400).json({
          message: 'Please enter your registered mobile no.',
          alert: 'error',
          user_id: 0
        });
      }

      // Validate mobile format (10 digits starting with 6-9)
      const mobileRegex = /^[6-9][0-9]{9}$/;
      if (!mobileRegex.test(login_mobile)) {
        return res.status(400).json({
          message: 'Please enter a valid mobile number',
          alert: 'error',
          user_id: 0
        });
      }

      // Find user by mobile - matches bidding_system query (mobile is numeric in real DB)
      const userQuery = await prisma.$queryRaw`
        SELECT u.*, e.organisation_name
        FROM public.users u
        LEFT JOIN public.enterprises e ON u.enterprise_id = e.id
        WHERE u.mobile::text = ${login_mobile}
        LIMIT 1
      `;

      const user = userQuery.length > 0 ? userQuery[0] : null;

      if (!user) {
        return res.json({
          message: 'Access denied!',
          alert: 'error',
          user_id: 0
        });
      }

      // Check if user is active
      if (user.status === 'inactive') {
        return res.json({
          message: 'Your account is locked. Please contact our system administrator!',
          alert: 'error',
          user_id: 0
        });
      }

      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000);

      // Delete any existing OTP for the user
      await prisma.$executeRaw`
        DELETE FROM public.user_otp
        WHERE user_id = ${user.id}::bigint
      `;

      // Insert new OTP - try with all common audit columns
      await prisma.$executeRaw`
        INSERT INTO public.user_otp (user_id, user_otp, created_at, updated_at, created_by, updated_by)
        VALUES (${user.id}::bigint, ${otp}::bigint, NOW(), NOW(), ${user.id}::bigint, ${user.id}::bigint)
      `;

      // TODO: Send SMS (implement SMS service)
      logger.info(`OTP for user ${user.id}: ${otp}`);

      return res.json({
        message: 'Credential matched!',
        alert: 'success',
        user_id: user.id
      });

    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({
        message: 'Server error occurred',
        alert: 'error',
        user_id: 0
      });
    }
  }

  // Verify OTP - matches bidding_system /bypass/login-otp
  static async verifyOtp(req, res) {
    try {
      const { user_id, user_otp } = req.body;

      if (!user_id || !user_otp) {
        return res.status(400).json({
          message: 'User ID and OTP are required',
          alert: 'error',
          user_id: user_id || 0,
          user_type: null
        });
      }

      // Check OTP validity (2 minutes window) - user_otp is bigint in real database
      const otpQuery = await prisma.$queryRaw`
        SELECT * FROM public.user_otp
        WHERE user_id = ${parseInt(user_id)}::bigint
        AND user_otp = ${parseInt(user_otp)}::bigint
        AND created_at >= NOW() - INTERVAL '2 minutes'
        ORDER BY id DESC
        LIMIT 1
      `;

      const validOtp = otpQuery.length > 0 ? otpQuery[0] : null;

      // Check for default OTP (for testing) - user_otp is bigint in real database
      const defaultOtpQuery = await prisma.$queryRaw`
        SELECT * FROM public.user_otp_default
        WHERE user_otp = ${parseInt(user_otp)}::bigint
        LIMIT 1
      `;

      const defaultOtp = defaultOtpQuery.length > 0 ? defaultOtpQuery[0] : null;

      if (!validOtp && !defaultOtp) {
        return res.status(400).json({
          message: 'OTP entered is invalid',
          alert: 'error',
          user_id: parseInt(user_id),
          user_type: null
        });
      }

      // Mark OTP as used by updating timestamp - simplified for real database
      await prisma.$executeRaw`
        UPDATE public.user_otp
        SET updated_at = NOW()
        WHERE user_id = ${parseInt(user_id)}::bigint
      `;

      // Get user details - matches bidding_system
      const userQuery = await prisma.$queryRaw`
        SELECT u.*, e.organisation_name
        FROM public.users u
        LEFT JOIN public.enterprises e ON u.enterprise_id = e.id
        WHERE u.id = ${parseInt(user_id)}::bigint
        LIMIT 1
      `;

      const user = userQuery.length > 0 ? userQuery[0] : null;

      if (!user) {
        return res.status(400).json({
          message: 'User not found',
          alert: 'error',
          user_id: parseInt(user_id),
          user_type: null
        });
      }

      // Generate JWT token for session
      const token = jwt.sign(
        { 
          userId: user.id,
          userType: user.user_type,
          enterpriseId: user.enterprise_id
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      // Log login track - matches bidding_system
      await prisma.$executeRaw`
        INSERT INTO public.login_track (user_id, user_enterprise_id, created_at, updated_at)
        VALUES (${user.id}::bigint, ${user.enterprise_id || null}::bigint, NOW(), NOW())
      `;

      // Return response based on user type - matches bidding_system exactly
      let redirectPath = '';
      if (user.user_type === 'admin' && user.status === 'active') {
        redirectPath = 'admin/dashboard';
      } else if (user.user_type === 'consignee' && user.status === 'active') {
        redirectPath = 'consignee/dashboard';
      } else if (user.user_type === 'fleet' && user.status === 'active') {
        redirectPath = 'supply-partner/dashboard';
      } else {
        return res.status(401).json({
          message: 'Account access denied',
          alert: 'error',
          user_id: parseInt(user_id),
          user_type: null
        });
      }

      return res.json({
        message: 'OTP entered is valid',
        alert: 'success',
        user_type: redirectPath,
        token: token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          mobile: user.mobile,
          user_type: user.user_type,
          enterprise_id: user.enterprise_id,
          organisation_name: user.organisation_name
        }
      });

    } catch (error) {
      logger.error('Verify OTP error:', error);
      res.status(500).json({
        message: 'Server error occurred',
        alert: 'error',
        user_id: parseInt(req.body.user_id) || 0,
        user_type: null
      });
    }
  }

  // Logout - matches bidding_system /bypass/user_logout
  static async logout(req, res) {
    try {
      const userId = req.user?.id;

      if (userId) {
        // Clear old OTPs - simplified for real database
        await prisma.$executeRaw`
          DELETE FROM public.user_otp
          WHERE user_id = ${userId}::bigint
          AND created_at < NOW() - INTERVAL '1 day'
        `;
      }

      res.json({
        success: true,
        message: 'Logged out successfully',
        redirect: '/sign-in'
      });

    } catch (error) {
      logger.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: 'Server error'
      });
    }
  }
}

module.exports = AuthController;
