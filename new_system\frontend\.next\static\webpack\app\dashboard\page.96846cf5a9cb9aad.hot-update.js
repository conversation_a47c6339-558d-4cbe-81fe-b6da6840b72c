"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* harmony import */ var _store_dashboardStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/dashboardStore */ \"(app-pages-browser)/./src/store/dashboardStore.ts\");\n/* harmony import */ var _store_socketStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/socketStore */ \"(app-pages-browser)/./src/store/socketStore.ts\");\n/* harmony import */ var _components_dashboard_DashboardHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DashboardHeader */ \"(app-pages-browser)/./src/components/dashboard/DashboardHeader.tsx\");\n/* harmony import */ var _components_dashboard_DashboardSummary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/DashboardSummary */ \"(app-pages-browser)/./src/components/dashboard/DashboardSummary.tsx\");\n/* harmony import */ var _components_dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/QuickActions */ \"(app-pages-browser)/./src/components/dashboard/QuickActions.tsx\");\n/* harmony import */ var _components_dashboard_ActivityFeed__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/ActivityFeed */ \"(app-pages-browser)/./src/components/dashboard/ActivityFeed.tsx\");\n/* harmony import */ var _components_dashboard_LoadAnalysis__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/LoadAnalysis */ \"(app-pages-browser)/./src/components/dashboard/LoadAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_BidAnalysis__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/BidAnalysis */ \"(app-pages-browser)/./src/components/dashboard/BidAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_TransporterParticipationAnalysis__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/TransporterParticipationAnalysis */ \"(app-pages-browser)/./src/components/dashboard/TransporterParticipationAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_TransporterAnalysis__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dashboard/TransporterAnalysis */ \"(app-pages-browser)/./src/components/dashboard/TransporterAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_CancellationSurvey__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dashboard/CancellationSurvey */ \"(app-pages-browser)/./src/components/dashboard/CancellationSurvey.tsx\");\n/* harmony import */ var _components_dashboard_BidPriceAnalysis__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/dashboard/BidPriceAnalysis */ \"(app-pages-browser)/./src/components/dashboard/BidPriceAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_PerformanceAnalysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/dashboard/PerformanceAnalysis */ \"(app-pages-browser)/./src/components/dashboard/PerformanceAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_SalesTrend__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/dashboard/SalesTrend */ \"(app-pages-browser)/./src/components/dashboard/SalesTrend.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isAuthenticated, isLoading, initializeAuth } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const { dashboardData, recentActivity, isLoading: dashboardLoading, initializeRealTimeUpdates, simulateRealTimeUpdates, stopRealTimeUpdates } = (0,_store_dashboardStore__WEBPACK_IMPORTED_MODULE_4__.useDashboardStore)();\n    const { connect, isConnected } = (0,_store_socketStore__WEBPACK_IMPORTED_MODULE_5__.useSocketStore)();\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, [\n        initializeAuth\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check localStorage for authentication as backup\n        const localToken = localStorage.getItem(\"token\");\n        const localAuth = localStorage.getItem(\"isAuthenticated\");\n        if (!isAuthenticated && !isLoading && !localToken && localAuth !== \"true\") {\n            console.log(\"Dashboard: User not authenticated, redirecting to login\");\n            router.push(\"/login\");\n            return;\n        } else if (isAuthenticated || localToken) {\n            console.log(\"Dashboard: User authenticated, staying on dashboard\");\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        user,\n        router\n    ]);\n    // Real-time updates effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            // Connect to WebSocket for real-time updates\n            connect();\n            // Initialize real-time updates\n            initializeRealTimeUpdates();\n            // Start simulation for demo purposes (remove in production)\n            simulateRealTimeUpdates();\n            // Cleanup on unmount\n            return ()=>{\n                stopRealTimeUpdates();\n            };\n        }\n    }, [\n        user,\n        connect,\n        initializeRealTimeUpdates,\n        simulateRealTimeUpdates,\n        stopRealTimeUpdates\n    ]);\n    // Show loading while checking authentication\n    if (!isAuthenticated || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-4 border-[#0e2d67] border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: isLoading ? \"Loading authentication...\" : \"Checking authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    // Dashboard loading is handled separately and shouldn't block the main dashboard\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardSummary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 flex items-center justify-between bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 w-3 rounded-full \".concat(isConnected ? \"bg-green-400 animate-pulse\" : \"bg-red-400\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: isConnected ? \"Real-time updates active\" : \"Connecting...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                        children: \"Live\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"Last updated: \",\n                                    dashboardData ? \"Just now\" : \"Never\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ActivityFeed__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_LoadAnalysis__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        title: \"Load Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BidAnalysis__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        title: \"Bid Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransporterParticipationAnalysis__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        title: \"Transporter Participation Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransporterAnalysis__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        title: \"Transporter Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CancellationSurvey__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        title: \"Cancellation Survey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BidPriceAnalysis__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        title: \"Bid Price Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_PerformanceAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        title: \"Performance Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SalesTrend__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        title: \"Sales Trend\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Enhanced Logistics Management System\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Real-time analytics and comprehensive dashboard for efficient logistics operations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-6 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 bg-[#0e2d67] rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Primary Color: #0e2d67\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 bg-[#e3000f] rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Secondary Color: #e3000f\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Real-time Updates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"ljo230LBIPL8zc2Fn5nCyxF0sZ0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore,\n        _store_dashboardStore__WEBPACK_IMPORTED_MODULE_4__.useDashboardStore,\n        _store_socketStore__WEBPACK_IMPORTED_MODULE_5__.useSocketStore\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});