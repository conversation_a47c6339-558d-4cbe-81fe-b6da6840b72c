'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Head from 'next/head';
import { PhoneIcon, KeyIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '@/store/authStore';

export default function LoginPage() {
  const router = useRouter();
  const { smsOtpLogin } = useAuthStore();
  const [step, setStep] = useState(1); // 1: mobile, 2: otp, 3: success
  const [formData, setFormData] = useState({
    login_mobile: '',
    user_otp: '',
    user_id: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Step 1: Send OTP
  const handleSendOtp = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/bypass/get-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ login_mobile: formData.login_mobile })
      });

      const data = await response.json();

      if (data.alert === 'success') {
        setFormData(prev => ({ ...prev, user_id: data.user_id }));
        setStep(2);
      } else {
        setError(data.message || 'Failed to send OTP');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Verify OTP
  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      console.log('Sending OTP verification request:', {
        user_id: formData.user_id,
        user_otp: formData.user_otp
      });

      const response = await fetch('/api/auth/bypass/login-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: formData.user_id,
          user_otp: formData.user_otp
        })
      });

      console.log('Response status:', response.status);
      const data = await response.json();
      console.log('Response data:', data);

      if (data.alert === 'success') {
        console.log('Authentication successful, response data:', data);

        // Show success step first
        setStep(3);

        // Store authentication data in localStorage as backup
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        localStorage.setItem('isAuthenticated', 'true');

        console.log('Stored authentication data in localStorage');

        // Immediate redirect using window.location (most reliable)
        const redirectUrl = '/dashboard';
        console.log('Redirecting immediately to:', redirectUrl);

        // Use window.location for immediate redirect
        window.location.href = redirectUrl;

      } else {
        console.log('Authentication failed:', data);
        setError(data.message || 'Invalid OTP');
      }
    } catch (err) {
      console.error('Network error during OTP verification:', err);
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0e2d67] via-[#1e3a8a] to-[#e3000f] flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-[#0e2d67] to-[#e3000f] rounded-full flex items-center justify-center mb-4">
            <PhoneIcon className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">eParivahan Login</h1>
          <p className="text-gray-600 mt-2">
            {step === 1 ? 'Enter your mobile number' :
             step === 2 ? 'Enter the OTP sent to your mobile' :
             'Login successful! Redirecting...'}
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {/* Success Message with Manual Redirect */}
        {step === 3 && (
          <>
            <Head>
              <meta httpEquiv="refresh" content="2;url=/dashboard" />
            </Head>
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
              <p className="mb-3">✅ Login successful! Redirecting to dashboard...</p>
              <p className="mb-3 text-sm">If not redirected automatically, click below:</p>
              <a
                href="/dashboard"
                className="inline-block bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Go to Dashboard
              </a>
            </div>
          </>
        )}

        {/* Step 1: Mobile Number */}
        {step === 1 && (
          <form onSubmit={handleSendOtp} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mobile Number <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <PhoneIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <input
                  type="tel"
                  value={formData.login_mobile}
                  onChange={(e) => setFormData(prev => ({ ...prev, login_mobile: e.target.value }))}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                  placeholder="Enter 10-digit mobile number"
                  pattern="[6-9][0-9]{9}"
                  maxLength="10"
                  required
                />
              </div>
              <p className="text-gray-500 text-sm mt-1">Enter mobile number starting with 6, 7, 8, or 9</p>
            </div>

            <button
              type="submit"
              disabled={isLoading || formData.login_mobile.length !== 10}
              className="w-full bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Sending OTP...
                </div>
              ) : (
                'Send OTP'
              )}
            </button>
          </form>
        )}

        {/* Step 2: OTP Verification */}
        {step === 2 && (
          <form onSubmit={handleVerifyOtp} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter OTP <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <KeyIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <input
                  type="text"
                  value={formData.user_otp}
                  onChange={(e) => setFormData(prev => ({ ...prev, user_otp: e.target.value }))}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent text-center text-lg tracking-widest"
                  placeholder="000000"
                  maxLength="6"
                  pattern="[0-9]{6}"
                  required
                />
              </div>
              <p className="text-gray-500 text-sm mt-1">
                OTP sent to +91 {formData.login_mobile}
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => setStep(1)}
                className="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={isLoading || formData.user_otp.length !== 6}
                className="flex-1 bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Verifying...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <CheckCircleIcon className="h-5 w-5 mr-2" />
                    Login
                  </div>
                )}
              </button>
            </div>
          </form>
        )}

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>© 2024 eParivahan. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
