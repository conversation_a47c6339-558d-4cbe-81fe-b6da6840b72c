'use client';

import { useEffect, useState } from 'react';
import {
  TruckIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyRupeeIcon,
  UserIcon,
  PhoneIcon,
  BuildingOfficeIcon,
  ClockIcon,
  DocumentTextIcon,
  PlusIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

// Enhanced universal admin create load component matching bidding_system exactly
function AdminCreateLoad() {
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    consigneeId: '',
    branchId: '',
    biddingType: '',
    loadType: 'public',
    pickupAddress: '',
    pickupLat: '',
    pickupLon: '',
    pickupName: '',
    pickupMobile: '',
    dropAddress: '',
    dropLat: '',
    dropLon: '',
    dropName: '',
    dropMobile: '',
    truckTypeId: '',
    materialType: '',
    weight: '',
    loadPrice: '',
    basePrice: '',
    loadDateTime: '',
    loadDateTimeTo: '',
    bidDateTime: '',
    comments: '',
    packaging: '',
    numberOfVehicles: '1',
    groupIds: [],
    fleetId: '',
    fleetownerPrice: ''
  });
  
  const [masterData, setMasterData] = useState({
    consignees: [],
    branches: [],
    truckTypes: [],
    materialTypes: [],
    fleetList: [],
    associations: []
  });
  
  const [errors, setErrors] = useState({});
  const [showPrivateGroup, setShowPrivateGroup] = useState(false);
  const [showIndentSection, setShowIndentSection] = useState(false);
  const [loadUnloadLocations, setLoadUnloadLocations] = useState([
    { address: '', type: '', latitude: '', longitude: '' }
  ]);

  // Fetch master data on component mount
  useEffect(() => {
    const fetchMasterData = async () => {
      try {
        const response = await fetch('/api/admin/create-load-data', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          setMasterData(data.data);
        }
      } catch (err) {
        console.error('Failed to fetch master data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMasterData();
  }, []);

  // Handle consignee selection and fetch phone
  const handleConsigneeChange = async (consigneeId) => {
    setFormData(prev => ({ ...prev, consigneeId }));
    
    try {
      const response = await fetch('/api/admin/get_consignee_phone', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ consigneeId })
      });
      
      if (response.ok) {
        const data = await response.json();
        const consigneeName = masterData.consignees.find(c => c.id === parseInt(consigneeId))?.organisationName || '';
        setFormData(prev => ({
          ...prev,
          pickupName: consigneeName,
          dropName: consigneeName,
          pickupMobile: data.consignee_phone || '',
          dropMobile: data.consignee_phone || ''
        }));
      }
    } catch (err) {
      console.error('Failed to fetch consignee phone:', err);
    }
  };

  // Handle load type change
  const handleLoadTypeChange = async (loadType) => {
    setFormData(prev => ({ ...prev, loadType }));
    setShowPrivateGroup(false);
    setShowIndentSection(false);
    
    if (loadType === 'private') {
      setShowPrivateGroup(true);
      // Fetch associations for the selected consignee
      if (formData.consigneeId) {
        try {
          const response = await fetch('/api/admin/shiper_association_List', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ consignee_id: formData.consigneeId })
          });
          
          if (response.ok) {
            const associations = await response.json();
            setMasterData(prev => ({ ...prev, associations }));
          }
        } catch (err) {
          console.error('Failed to fetch associations:', err);
        }
      }
    } else if (loadType === 'indent') {
      setShowIndentSection(true);
    }
  };

  // Add more load/unload locations
  const addLoadUnloadLocation = () => {
    setLoadUnloadLocations(prev => [
      ...prev,
      { address: '', type: '', latitude: '', longitude: '' }
    ]);
  };

  // Remove load/unload location
  const removeLoadUnloadLocation = (index) => {
    setLoadUnloadLocations(prev => prev.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Validate form data
      const newErrors = {};
      
      if (!formData.consigneeId) newErrors.consigneeId = 'Please select a shipper';
      if (!formData.pickupAddress) newErrors.pickupAddress = 'Please enter pickup location';
      if (!formData.dropAddress) newErrors.dropAddress = 'Please enter drop location';
      if (!formData.truckTypeId) newErrors.truckTypeId = 'Please select truck type';
      if (!formData.weight) newErrors.weight = 'Please enter weight';
      
      if (formData.loadType === 'private' && (!formData.groupIds || formData.groupIds.length === 0)) {
        newErrors.groupIds = 'Please select an association';
      }
      
      if (formData.loadType === 'indent') {
        if (!formData.fleetId) newErrors.fleetId = 'Please select a supply partner';
        if (!formData.fleetownerPrice) newErrors.fleetownerPrice = 'Please enter supplier price';
      }
      
      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setIsLoading(false);
        return;
      }
      
      // Submit form data
      const response = await fetch('/api/admin/create-load', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          loadUnloadLocations
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        // Show success message and redirect
        alert('Load created successfully!');
        window.location.href = '/admin/load_list';
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to create load');
      }
    } catch (err) {
      console.error('Failed to create load:', err);
      alert('Failed to create load. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-[#0e2d67] mx-auto mb-6"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Create Load Form</h2>
          <p className="text-gray-600">Fetching form data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Create Load</h1>
          <p className="text-lg text-gray-600">
            Create a new load for bidding in the transport marketplace
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Shipper Selection */}
              <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <div className="flex items-center mb-4">
                  <BuildingOfficeIcon className="h-6 w-6 text-[#0e2d67] mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900">Shipper Information</h3>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Shipper <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={formData.consigneeId}
                      onChange={(e) => handleConsigneeChange(e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent ${
                        errors.consigneeId ? 'border-red-500' : 'border-gray-300'
                      }`}
                      required
                    >
                      <option value="">Choose a shipper</option>
                      {masterData.consignees.map((consignee) => (
                        <option key={consignee.id} value={consignee.id}>
                          {consignee.organisationName}
                          {consignee.isVerified && ' ✓'}
                        </option>
                      ))}
                    </select>
                    {errors.consigneeId && (
                      <p className="text-red-500 text-sm mt-1">{errors.consigneeId}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Branch <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={formData.branchId}
                      onChange={(e) => setFormData(prev => ({ ...prev, branchId: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                      required
                    >
                      <option value="">Choose a branch</option>
                      {masterData.branches.map((branch) => (
                        <option key={branch.id} value={branch.id}>
                          {branch.name} - {branch.enterprise?.organisationName}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bidding Type <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={formData.biddingType}
                      onChange={(e) => setFormData(prev => ({ ...prev, biddingType: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                      required
                    >
                      <option value="">Select Bidding Type</option>
                      <option value="0">Spot</option>
                      <option value="1">Contractual</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Load Type Selection */}
              <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <div className="flex items-center mb-4">
                  <TruckIcon className="h-6 w-6 text-[#e3000f] mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900">Load Type</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    {['public', 'private', 'indent'].map((type) => (
                      <label key={type} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="loadType"
                          value={type}
                          checked={formData.loadType === type}
                          onChange={(e) => handleLoadTypeChange(e.target.value)}
                          className="text-[#0e2d67] focus:ring-[#0e2d67]"
                        />
                        <span className="text-sm font-medium text-gray-700 capitalize">{type}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Private Group Association Selection */}
          {showPrivateGroup && (
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="flex items-center mb-4">
                <UserIcon className="h-6 w-6 text-purple-600 mr-2" />
                <h3 className="text-xl font-semibold text-gray-900">Select Association</h3>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Choose Associations <span className="text-red-500">*</span>
                </label>
                <select
                  multiple
                  value={formData.groupIds}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    groupIds: Array.from(e.target.selectedOptions, option => option.value)
                  }))}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent min-h-[120px] ${
                    errors.groupIds ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  {masterData.associations.map((association) => (
                    <option key={association.id} value={association.id}>
                      {association.association_name} (Partners: {association.number_of_fleet})
                    </option>
                  ))}
                </select>
                {errors.groupIds && (
                  <p className="text-red-500 text-sm mt-1">{errors.groupIds}</p>
                )}
                <p className="text-gray-500 text-sm mt-1">Hold Ctrl/Cmd to select multiple associations</p>
              </div>
            </div>
          )}

          {/* Indent Section - Supply Partner */}
          {showIndentSection && (
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="flex items-center mb-4">
                <TruckIcon className="h-6 w-6 text-green-600 mr-2" />
                <h3 className="text-xl font-semibold text-gray-900">Supply Partner</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Supply Partner <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.fleetId}
                    onChange={(e) => setFormData(prev => ({ ...prev, fleetId: e.target.value }))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent ${
                      errors.fleetId ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Choose a Supply Partner</option>
                    {masterData.fleetList.map((fleet) => (
                      <option key={fleet.id} value={fleet.id}>
                        {fleet.organisationName}
                        {fleet.isVerified && ' ✓'}
                      </option>
                    ))}
                  </select>
                  {errors.fleetId && (
                    <p className="text-red-500 text-sm mt-1">{errors.fleetId}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Supplier Price <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <CurrencyRupeeIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    <input
                      type="number"
                      value={formData.fleetownerPrice}
                      onChange={(e) => setFormData(prev => ({ ...prev, fleetownerPrice: e.target.value }))}
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent ${
                        errors.fleetownerPrice ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter supplier price"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  {errors.fleetownerPrice && (
                    <p className="text-red-500 text-sm mt-1">{errors.fleetownerPrice}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Location Information */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <MapPinIcon className="h-6 w-6 text-[#0e2d67] mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Location Information</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Pickup Location */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-800 flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  Pickup Location
                </h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pickup Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.pickupAddress}
                    onChange={(e) => setFormData(prev => ({ ...prev, pickupAddress: e.target.value }))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent ${
                      errors.pickupAddress ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter pickup location"
                    required
                  />
                  {errors.pickupAddress && (
                    <p className="text-red-500 text-sm mt-1">{errors.pickupAddress}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Name</label>
                    <input
                      type="text"
                      value={formData.pickupName}
                      onChange={(e) => setFormData(prev => ({ ...prev, pickupName: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                      placeholder="Contact person name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Mobile</label>
                    <input
                      type="tel"
                      value={formData.pickupMobile}
                      onChange={(e) => setFormData(prev => ({ ...prev, pickupMobile: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                      placeholder="Mobile number"
                      pattern="[6789][0-9]{9}"
                      maxLength="10"
                    />
                  </div>
                </div>
              </div>

              {/* Drop Location */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-800 flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  Drop Location
                </h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Drop Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.dropAddress}
                    onChange={(e) => setFormData(prev => ({ ...prev, dropAddress: e.target.value }))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent ${
                      errors.dropAddress ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter drop location"
                    required
                  />
                  {errors.dropAddress && (
                    <p className="text-red-500 text-sm mt-1">{errors.dropAddress}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Name</label>
                    <input
                      type="text"
                      value={formData.dropName}
                      onChange={(e) => setFormData(prev => ({ ...prev, dropName: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                      placeholder="Contact person name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Mobile</label>
                    <input
                      type="tel"
                      value={formData.dropMobile}
                      onChange={(e) => setFormData(prev => ({ ...prev, dropMobile: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                      placeholder="Mobile number"
                      pattern="[6789][0-9]{9}"
                      maxLength="10"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Load/Unload Locations */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <MapPinIcon className="h-6 w-6 text-purple-600 mr-2" />
                <h3 className="text-xl font-semibold text-gray-900">Additional Load/Unload Locations</h3>
              </div>
              <button
                type="button"
                onClick={addLoadUnloadLocation}
                className="inline-flex items-center px-4 py-2 bg-[#0e2d67] text-white rounded-lg hover:bg-[#0e2d67]/90 transition-colors"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                Add More
              </button>
            </div>

            <div className="space-y-4">
              {loadUnloadLocations.map((location, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-gray-200 rounded-lg">
                  <div>
                    <input
                      type="text"
                      value={location.address}
                      onChange={(e) => {
                        const newLocations = [...loadUnloadLocations];
                        newLocations[index].address = e.target.value;
                        setLoadUnloadLocations(newLocations);
                      }}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <select
                      value={location.type}
                      onChange={(e) => {
                        const newLocations = [...loadUnloadLocations];
                        newLocations[index].type = e.target.value;
                        setLoadUnloadLocations(newLocations);
                      }}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                    >
                      <option value="">Select Type</option>
                      <option value="L">Load</option>
                      <option value="U">Unload</option>
                    </select>
                  </div>
                  <div className="flex items-center">
                    {index > 0 && (
                      <button
                        type="button"
                        onClick={() => removeLoadUnloadLocation(index)}
                        className="inline-flex items-center px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                      >
                        <XMarkIcon className="h-5 w-5 mr-1" />
                        Remove
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Load Details */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <DocumentTextIcon className="h-6 w-6 text-[#e3000f] mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Load Details</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Truck Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.truckTypeId}
                  onChange={(e) => setFormData(prev => ({ ...prev, truckTypeId: e.target.value }))}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent ${
                    errors.truckTypeId ? 'border-red-500' : 'border-gray-300'
                  }`}
                  required
                >
                  <option value="">Select Truck Type</option>
                  {masterData.truckTypes.map((truck) => (
                    <option key={truck.id} value={truck.id}>
                      {truck.name} ({truck.capacity})
                    </option>
                  ))}
                </select>
                {errors.truckTypeId && (
                  <p className="text-red-500 text-sm mt-1">{errors.truckTypeId}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Material Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.materialType}
                  onChange={(e) => setFormData(prev => ({ ...prev, materialType: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                  required
                >
                  <option value="">Select Material Type</option>
                  {masterData.materialTypes.map((material) => (
                    <option key={material.id} value={material.id}>
                      {material.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weight (in tons) <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  value={formData.weight}
                  onChange={(e) => setFormData(prev => ({ ...prev, weight: e.target.value }))}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent ${
                    errors.weight ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter weight"
                  min="0"
                  step="0.01"
                  required
                />
                {errors.weight && (
                  <p className="text-red-500 text-sm mt-1">{errors.weight}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Number of Vehicles</label>
                <input
                  type="number"
                  value={formData.numberOfVehicles}
                  onChange={(e) => setFormData(prev => ({ ...prev, numberOfVehicles: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                  placeholder="Number of vehicles"
                  min="1"
                  defaultValue="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Packaging</label>
                <input
                  type="text"
                  value={formData.packaging}
                  onChange={(e) => setFormData(prev => ({ ...prev, packaging: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                  placeholder="Packaging details"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Unloading Weight</label>
                <input
                  type="number"
                  value={formData.unloadingWeight}
                  onChange={(e) => setFormData(prev => ({ ...prev, unloadingWeight: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                  placeholder="Unloading weight"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
          </div>

          {/* Pricing Information */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <CurrencyRupeeIcon className="h-6 w-6 text-green-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Pricing Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Load Price</label>
                <div className="relative">
                  <CurrencyRupeeIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="number"
                    value={formData.loadPrice}
                    onChange={(e) => setFormData(prev => ({ ...prev, loadPrice: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                    placeholder="Enter load price"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Base Price</label>
                <div className="relative">
                  <CurrencyRupeeIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="number"
                    value={formData.basePrice}
                    onChange={(e) => setFormData(prev => ({ ...prev, basePrice: e.target.value }))}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                    placeholder="Enter base price"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Date and Time Information */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <CalendarIcon className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Schedule Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Load Date & Time <span className="text-red-500">*</span>
                </label>
                <input
                  type="datetime-local"
                  value={formData.loadDateTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, loadDateTime: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Load Date To</label>
                <input
                  type="datetime-local"
                  value={formData.loadDateTimeTo}
                  onChange={(e) => setFormData(prev => ({ ...prev, loadDateTimeTo: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                />
              </div>

              {formData.loadType !== 'indent' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bid Date & Time <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.bidDateTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, bidDateTime: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                    required
                  />
                </div>
              )}
            </div>
          </div>

          {/* Comments */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <DocumentTextIcon className="h-6 w-6 text-gray-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Additional Information</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Comments</label>
              <textarea
                value={formData.comments}
                onChange={(e) => setFormData(prev => ({ ...prev, comments: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent"
                rows="4"
                placeholder="Enter any additional comments or instructions..."
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => window.history.back()}
              className="px-8 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-8 py-3 bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Creating Load...
                </div>
              ) : (
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-2" />
                  Create Load
                </div>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function AdminCreateLoadPage() {
  return <AdminCreateLoad />;
}
