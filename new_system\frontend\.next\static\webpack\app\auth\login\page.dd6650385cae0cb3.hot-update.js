"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// SMS OTP Login Schema (Mobile-only login as per legacy system)\nconst mobileLoginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    mobile: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().regex(/^[6-9]\\d{9}$/, \"Mobile number must be 10 digits starting with 6-9\").length(10, \"Mobile number must be exactly 10 digits\")\n});\n// OTP Verification Schema\nconst otpVerificationSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    otp: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().length(6, \"OTP must be exactly 6 digits\").regex(/^\\d{6}$/, \"OTP must contain only numbers\")\n});\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoading } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    const [mobile, setMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [otpSent, setOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Mobile login form\n    const mobileForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(mobileLoginSchema)\n    });\n    // OTP verification form\n    const otpForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(otpVerificationSchema)\n    });\n    // Send OTP\n    const onMobileSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/get-otp\", {\n                login_mobile: data.mobile\n            });\n            if (response.data.alert === \"success\") {\n                setMobile(data.mobile);\n                setUserId(response.data.user_id);\n                setStep(\"otp\");\n                setOtpSent(true);\n                setCountdown(120); // 2 minutes countdown\n                // Start countdown timer\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP sent to your mobile number!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.message || \"Failed to send OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to send OTP\");\n        }\n    };\n    // Verify OTP\n    const onOtpSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/login-otp\", {\n                user_id: userId,\n                user_otp: data.otp\n            });\n            if (response.data.success) {\n                const { tokens, user } = response.data.data;\n                // Update auth store state (this will also handle localStorage/cookies)\n                const authStore = _store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore.getState();\n                authStore.smsOtpLogin(user, tokens.accessToken, tokens.refreshToken);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Login successful!\");\n                // Redirect to main dashboard (all user types use the same dashboard)\n                router.push(\"/dashboard\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.error || \"Invalid OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"OTP verification failed\");\n        }\n    };\n    // Resend OTP\n    const resendOtp = async ()=>{\n        if (countdown > 0) return;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/get-otp\", {\n                login_mobile: mobile\n            });\n            if (response.data.alert === \"success\") {\n                setCountdown(120);\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP resent successfully!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to resend OTP\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                        backgroundSize: \"60px 60px\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 w-16 rounded-2xl bg-gradient-to-br from-[#0e2d67] to-[#1a4480] flex items-center justify-center shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-[#e3000f] flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-xs\",\n                                        children: \"EP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: step === \"mobile\" ? \"Welcome to eParivahan\" : \"Verify OTP\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: step === \"mobile\" ? \"Enter your mobile number to receive OTP\" : \"OTP sent to \".concat(mobile, \". Enter the 6-digit code.\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8\",\n                    children: [\n                        step === \"mobile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: mobileForm.handleSubmit(onMobileSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"mobile\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Mobile Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...mobileForm.register(\"mobile\"),\n                                                    type: \"tel\",\n                                                    autoComplete: \"tel\",\n                                                    maxLength: 10,\n                                                    className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(mobileForm.formState.errors.mobile ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-lg tracking-wider\"),\n                                                    placeholder: \"9876543210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"+91\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        mobileForm.formState.errors.mobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this),\n                                                mobileForm.formState.errors.mobile.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Sending OTP...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Send OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this),\n                        step === \"otp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: otpForm.handleSubmit(onOtpSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"otp\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Enter OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...otpForm.register(\"otp\"),\n                                                type: \"text\",\n                                                maxLength: 6,\n                                                className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(otpForm.formState.errors.otp ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-center text-2xl tracking-widest font-mono\"),\n                                                placeholder: \"000000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        otpForm.formState.errors.otp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this),\n                                                otpForm.formState.errors.otp.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this),\n                                countdown > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Resend OTP in \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-[#0e2d67]\",\n                                                children: [\n                                                    Math.floor(countdown / 60),\n                                                    \":\",\n                                                    (countdown % 60).toString().padStart(2, \"0\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verifying...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verify & Login\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: resendOtp,\n                                            disabled: countdown > 0,\n                                            className: \"w-full border-2 border-gray-200 text-gray-700 font-semibold py-2 px-4 rounded-xl hover:border-[#0e2d67] hover:text-[#0e2d67] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 bg-white/50 backdrop-blur-sm\",\n                                            children: countdown > 0 ? \"Please wait...\" : \"Resend OTP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                setStep(\"mobile\");\n                                                setCountdown(0);\n                                                otpForm.reset();\n                                            },\n                                            className: \"w-full text-gray-600 font-medium py-2 px-4 rounded-xl hover:text-[#0e2d67] focus:outline-none transition-all duration-200\",\n                                            children: \"← Change Mobile Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-100 rounded-2xl p-6 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg bg-blue-500 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"\\uD83D\\uDD11\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-bold text-blue-900\",\n                                    children: \"Demo Login Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Admin User\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8240301895\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Consignee (Load Poster)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9874896900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8181816477\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9051720133\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs font-semibold text-blue-800 mb-2\",\n                                    children: \"Default OTPs (Admin Access)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-2\",\n                                    children: [\n                                        \"123456\",\n                                        \"111111\",\n                                        \"000000\",\n                                        \"999999\",\n                                        \"555555\"\n                                    ].map((otp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-2 text-center border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs font-mono text-blue-900\",\n                                                children: otp\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, otp, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-2\",\n                                    children: \"These OTPs work for any mobile number for testing purposes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"ggp3cQO+eSU+p6y29aLI5bJzFZc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ })

});