const { PrismaClient } = require('@prisma/client');
const { validationResult } = require('express-validator');
const logger = require('../../utils/logger');

const prisma = new PrismaClient();

class AdminLoadController {
  /**
   * Get load list
   */
  static async getLoadList(req, res) {
    try {
      const { page = 1, limit = 20, status, search, dateFrom, dateTo } = req.query;
      const offset = (page - 1) * limit;
      
      let whereClause = {};
      
      if (status && status !== 'all') {
        whereClause.status = status;
      }
      
      if (search) {
        whereClause.OR = [
          { pickupLocationAddress: { contains: search, mode: 'insensitive' } },
          { dropLocationAddress: { contains: search, mode: 'insensitive' } },
          { consigneeEnterprise: { organisationName: { contains: search, mode: 'insensitive' } } }
        ];
      }
      
      if (dateFrom && dateTo) {
        whereClause.loadDate = {
          gte: new Date(dateFrom),
          lte: new Date(dateTo)
        };
      }

      const [loads, totalCount] = await Promise.all([
        prisma.load.findMany({
          where: whereClause,
          include: {
            consigneeEnterprise: {
              select: { id: true, organisationName: true }
            },
            consignee: {
              select: { id: true, name: true }
            },
            truckType: {
              select: { id: true, name: true, capacity: true }
            },
            quotations: {
              select: {
                id: true,
                quotedPrice: true,
                status: true,
                enterprise: {
                  select: { organisationName: true }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: parseInt(limit)
        }),
        prisma.load.count({ where: whereClause })
      ]);

      res.json({
        success: true,
        data: {
          loads,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(totalCount / limit),
            totalCount,
            hasNext: offset + loads.length < totalCount,
            hasPrev: page > 1
          }
        }
      });
    } catch (error) {
      logger.error('Get load list error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch load list'
      });
    }
  }

  /**
   * Get create load page data
   */
  static async getCreatLoadPage(req, res) {
    try {
      // Get all active truck types
      const truckTypes = await prisma.truckType.findMany({
        where: { status: 'ACTIVE' },
        select: { id: true, name: true, capacity: true },
        orderBy: { name: 'asc' }
      });

      // Get all active material types
      const materialTypes = await prisma.materialType.findMany({
        where: { status: 'ACTIVE' },
        select: { id: true, name: true },
        orderBy: { name: 'asc' }
      });

      // Get all active consignees (shippers) with their enterprise details - matches bidding_system exactly
      const consigneesQuery = await prisma.user.findMany({
        where: {
          userType: 'CONSIGNEE',
          status: 'ACTIVE'
        },
        include: {
          enterprise: {
            select: {
              id: true,
              organisationName: true,
              isVerified: true,
              status: true
            }
          }
        },
        orderBy: {
          enterprise: {
            organisationName: 'desc'
          }
        }
      });

      // Filter and format consignees exactly like bidding_system
      const consignees = consigneesQuery
        .filter(consignee => consignee.enterprise && consignee.enterprise.status === 'ACTIVE')
        .map(consignee => ({
          consignee_id: consignee.id,
          enterprises_id: consignee.enterprise.id,
          consignee_name: consignee.name,
          consignee_phone: consignee.mobile,
          organisation_name: consignee.enterprise.organisationName,
          is_verified: consignee.enterprise.isVerified === 'YES'
        }));

      // Get fleet list for indent loads - matches bidding_system exactly
      const fleetListQuery = await prisma.enterprise.findMany({
        where: {
          type: 'FLEET',
          status: 'ACTIVE'
        },
        select: {
          id: true,
          organisationName: true,
          isVerified: true
        },
        orderBy: {
          organisationName: 'desc'
        }
      });

      const fleetList = fleetListQuery.map(fleet => ({
        id: fleet.id,
        enterprise_id: fleet.id,
        organisationName: fleet.organisationName,
        is_verified: fleet.isVerified === 'YES'
      }));

      // Get branches (empty initially, will be populated when shipper is selected)
      const branches = [];

      logger.info(`Returning create load data: ${consignees.length} consignees, ${truckTypes.length} truck types, ${materialTypes.length} material types, ${fleetList.length} fleet partners`);

      res.json({
        success: true,
        data: {
          consignees,
          truckTypes,
          materialTypes,
          fleetList,
          branches
        }
      });

    } catch (error) {
      logger.error('Get create load page error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get create load page data'
      });
    }
  }

  /**
   * Create new load
   */
  static async postCreateLoad(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const {
        consigneeId,
        consigneeEnterpriseId,
        branchId,
        truckTypeId,
        loadDate,
        loadDateTime,
        basePrice,
        pickupLocationAddress,
        pickupLocationLatitude,
        pickupLocationLongitude,
        dropLocationAddress,
        dropLocationLatitude,
        dropLocationLongitude,
        pickupPersonName,
        pickupPersonMobile,
        dropPersonName,
        dropPersonMobile,
        materialType,
        packaging,
        weight,
        distance,
        isContractual = 0,
        bidDate,
        bidTime,
        bidEndTime,
        bidIncrement,
        comments
      } = req.body;

      const load = await prisma.load.create({
        data: {
          consigneeId: parseInt(consigneeId),
          consigneeEnterpriseId: parseInt(consigneeEnterpriseId),
          branchId: branchId ? parseInt(branchId) : null,
          truckTypeId: parseInt(truckTypeId),
          loadDate: new Date(loadDate),
          loadDateTime: loadDateTime ? new Date(loadDateTime) : null,
          basePrice: parseFloat(basePrice),
          pickupLocationAddress,
          pickupLocationLatitude: pickupLocationLatitude ? parseFloat(pickupLocationLatitude) : null,
          pickupLocationLongitude: pickupLocationLongitude ? parseFloat(pickupLocationLongitude) : null,
          dropLocationAddress,
          dropLocationLatitude: parseFloat(dropLocationLatitude),
          dropLocationLongitude: parseFloat(dropLocationLongitude),
          pickupPersonName,
          pickupPersonMobile,
          dropPersonName,
          dropPersonMobile,
          materialType,
          packaging,
          weight: weight ? parseFloat(weight) : null,
          distance: distance ? parseFloat(distance) : null,
          isContractual: parseInt(isContractual),
          bidDate: bidDate ? new Date(bidDate) : null,
          bidTime,
          bidEndTime: bidEndTime ? new Date(bidEndTime) : null,
          bidIncrement: bidIncrement ? parseInt(bidIncrement) : null,
          consigneeComments: comments,
          status: 'ACTIVE',
          createdBy: req.user.id,
          updatedBy: req.user.id
        },
        include: {
          consigneeEnterprise: {
            select: { organisationName: true }
          },
          consignee: {
            select: { name: true }
          },
          truckType: {
            select: { name: true, capacity: true }
          }
        }
      });

      // Emit real-time notification for new load
      if (req.app.locals.io) {
        req.app.locals.io.emit('new-load', {
          loadId: load.id,
          message: `New load created: ${load.pickupLocationAddress} to ${load.dropLocationAddress}`,
          load
        });
      }

      res.status(201).json({
        success: true,
        message: 'Load created successfully',
        data: load
      });
    } catch (error) {
      logger.error('Create load error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create load'
      });
    }
  }

  /**
   * Get edit load page data
   */
  static async getEditLoadPage(req, res) {
    try {
      const { id } = req.params;
      
      const [load, truckTypes, materialTypes, enterprises] = await Promise.all([
        prisma.load.findUnique({
          where: { id: parseInt(id) },
          include: {
            consigneeEnterprise: {
              select: { id: true, organisationName: true }
            },
            consignee: {
              select: { id: true, name: true }
            },
            truckType: {
              select: { id: true, name: true, capacity: true }
            },
            branch: {
              select: { id: true, name: true }
            }
          }
        }),
        prisma.truckType.findMany({
          where: { status: 'ACTIVE' },
          select: { id: true, name: true, capacity: true }
        }),
        prisma.materialType.findMany({
          where: { status: 'ACTIVE' },
          select: { id: true, name: true }
        }),
        prisma.enterprise.findMany({
          where: { 
            status: 'ACTIVE',
            type: 'CONSIGNEE'
          },
          select: { id: true, organisationName: true }
        })
      ]);

      if (!load) {
        return res.status(404).json({
          success: false,
          error: 'Load not found'
        });
      }

      res.json({
        success: true,
        data: {
          load,
          truckTypes,
          materialTypes,
          enterprises
        }
      });
    } catch (error) {
      logger.error('Get edit load page error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch load data'
      });
    }
  }

  /**
   * Update existing load
   */
  static async postUpdateLoad(req, res) {
    try {
      const { id } = req.params;
      const updateData = { ...req.body };
      
      // Remove id from update data
      delete updateData.id;
      
      // Convert string numbers to appropriate types
      if (updateData.consigneeId) updateData.consigneeId = parseInt(updateData.consigneeId);
      if (updateData.consigneeEnterpriseId) updateData.consigneeEnterpriseId = parseInt(updateData.consigneeEnterpriseId);
      if (updateData.truckTypeId) updateData.truckTypeId = parseInt(updateData.truckTypeId);
      if (updateData.basePrice) updateData.basePrice = parseFloat(updateData.basePrice);
      if (updateData.weight) updateData.weight = parseFloat(updateData.weight);
      if (updateData.distance) updateData.distance = parseFloat(updateData.distance);
      if (updateData.loadDate) updateData.loadDate = new Date(updateData.loadDate);
      if (updateData.bidDate) updateData.bidDate = new Date(updateData.bidDate);
      if (updateData.bidEndTime) updateData.bidEndTime = new Date(updateData.bidEndTime);
      
      updateData.updatedBy = req.user.id;

      const updatedLoad = await prisma.load.update({
        where: { id: parseInt(id) },
        data: updateData,
        include: {
          consigneeEnterprise: {
            select: { organisationName: true }
          },
          consignee: {
            select: { name: true }
          },
          truckType: {
            select: { name: true, capacity: true }
          }
        }
      });

      res.json({
        success: true,
        message: 'Load updated successfully',
        data: updatedLoad
      });
    } catch (error) {
      logger.error('Update load error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update load'
      });
    }
  }

  /**
   * View load details
   */
  static async viewLoadDetails(req, res) {
    try {
      const { id } = req.params;
      
      const load = await prisma.load.findUnique({
        where: { id: parseInt(id) },
        include: {
          consigneeEnterprise: {
            select: { id: true, organisationName: true, contactPersonName: true, contactPersonMobile: true }
          },
          consignee: {
            select: { id: true, name: true, mobile: true }
          },
          truckType: {
            select: { id: true, name: true, capacity: true }
          },
          branch: {
            select: { id: true, name: true, city: true, state: true }
          },
          quotations: {
            include: {
              enterprise: {
                select: { id: true, organisationName: true }
              },
              vehicle: {
                select: { id: true, vehicleNumber: true, driverName: true, driverMobile: true }
              }
            },
            orderBy: { quotedPrice: 'asc' }
          },
          vehicleTrackings: {
            include: {
              vehicle: {
                select: { vehicleNumber: true }
              }
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      if (!load) {
        return res.status(404).json({
          success: false,
          error: 'Load not found'
        });
      }

      res.json({
        success: true,
        data: load
      });
    } catch (error) {
      logger.error('View load details error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch load details'
      });
    }
  }

  /**
   * Select quotation for load
   */
  static async adminSelectQuote(req, res) {
    try {
      const { id } = req.params;
      const { quotationId, selectedPrice } = req.body;

      // Update quotation status
      await prisma.quotation.update({
        where: { id: parseInt(quotationId) },
        data: { 
          status: 'ACCEPTED',
          updatedBy: req.user.id
        }
      });

      // Update load with selected quotation details
      const updatedLoad = await prisma.load.update({
        where: { id: parseInt(id) },
        data: {
          fleetownerPrice: parseFloat(selectedPrice),
          status: 'CONFIRMED',
          consigneeConfirmedAt: new Date(),
          consigneeConfirmedBy: req.user.id,
          updatedBy: req.user.id
        },
        include: {
          quotations: {
            where: { id: parseInt(quotationId) },
            include: {
              enterprise: {
                select: { organisationName: true }
              }
            }
          }
        }
      });

      // Reject other quotations
      await prisma.quotation.updateMany({
        where: {
          loadId: parseInt(id),
          id: { not: parseInt(quotationId) }
        },
        data: { 
          status: 'REJECTED',
          updatedBy: req.user.id
        }
      });

      // Emit real-time notification
      if (req.app.locals.io) {
        req.app.locals.io.to(`load-${id}`).emit('quote-selected', {
          loadId: parseInt(id),
          quotationId: parseInt(quotationId),
          message: 'Quotation has been selected for this load'
        });
      }

      res.json({
        success: true,
        message: 'Quotation selected successfully',
        data: updatedLoad
      });
    } catch (error) {
      logger.error('Select quote error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to select quotation'
      });
    }
  }

  /**
   * Cancel load
   */
  static async adminCancelLoad(req, res) {
    try {
      const { id } = req.params;
      const { cancelReason, cancelComment } = req.body;

      const cancelledLoad = await prisma.load.update({
        where: { id: parseInt(id) },
        data: {
          status: 'CANCELLED',
          cancelComment,
          updatedBy: req.user.id
        }
      });

      // Reject all pending quotations
      await prisma.quotation.updateMany({
        where: {
          loadId: parseInt(id),
          status: 'INITIATED'
        },
        data: { 
          status: 'REJECTED',
          updatedBy: req.user.id
        }
      });

      // Emit real-time notification
      if (req.app.locals.io) {
        req.app.locals.io.to(`load-${id}`).emit('load-cancelled', {
          loadId: parseInt(id),
          message: 'Load has been cancelled',
          reason: cancelReason
        });
      }

      res.json({
        success: true,
        message: 'Load cancelled successfully',
        data: cancelledLoad
      });
    } catch (error) {
      logger.error('Cancel load error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to cancel load'
      });
    }
  }

  /**
   * Start trip for load
   */
  static async startTrip(req, res) {
    try {
      const { id } = req.params;
      const { vehicleId, driverName, driverPhone } = req.body;

      const updatedLoad = await prisma.load.update({
        where: { id: parseInt(id) },
        data: {
          vehicleId: vehicleId ? parseInt(vehicleId) : null,
          driverName,
          driverPhone,
          status: 'IN_TRANSIT',
          fleetReportedAt: new Date(),
          fleetReportedBy: req.user.id,
          updatedBy: req.user.id
        },
        include: {
          vehicle: {
            select: { vehicleNumber: true }
          }
        }
      });

      // Create initial vehicle tracking entry
      if (vehicleId) {
        await prisma.vehicleTracking.create({
          data: {
            vehicleId: parseInt(vehicleId),
            loadId: parseInt(id),
            userId: req.user.id,
            status: 'TRIP_STARTED',
            comment: 'Trip started by admin',
            createdBy: req.user.id,
            updatedBy: req.user.id
          }
        });
      }

      // Emit real-time notification
      if (req.app.locals.io) {
        req.app.locals.io.to(`load-${id}`).emit('trip-started', {
          loadId: parseInt(id),
          message: 'Trip has been started',
          driverName,
          vehicleNumber: updatedLoad.vehicle?.vehicleNumber
        });
      }

      res.json({
        success: true,
        message: 'Trip started successfully',
        data: updatedLoad
      });
    } catch (error) {
      logger.error('Start trip error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to start trip'
      });
    }
  }

  // Helper methods for load creation
  static async gettruckType(req, res) {
    try {
      const truckTypes = await prisma.truckType.findMany({
        where: { status: 'ACTIVE' },
        select: { id: true, name: true, capacity: true }
      });

      res.json({
        success: true,
        data: truckTypes
      });
    } catch (error) {
      logger.error('Get truck types error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch truck types'
      });
    }
  }

  static async getmaterialType(req, res) {
    try {
      const materialTypes = await prisma.materialType.findMany({
        where: { status: 'ACTIVE' },
        select: { id: true, name: true }
      });

      res.json({
        success: true,
        data: materialTypes
      });
    } catch (error) {
      logger.error('Get material types error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch material types'
      });
    }
  }

  static async getconsignee(req, res) {
    try {
      const consignees = await prisma.user.findMany({
        where: { 
          userType: 'CONSIGNEE',
          status: 'ACTIVE'
        },
        select: { 
          id: true, 
          name: true, 
          mobile: true,
          enterprise: {
            select: { id: true, organisationName: true }
          }
        }
      });

      res.json({
        success: true,
        data: consignees
      });
    } catch (error) {
      logger.error('Get consignees error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch consignees'
      });
    }
  }

  static async getfleetList(req, res) {
    try {
      const fleets = await prisma.enterprise.findMany({
        where: {
          type: 'FLEET',
          status: 'ACTIVE'
        },
        select: {
          id: true,
          organisationName: true,
          contactPersonName: true,
          contactPersonMobile: true
        }
      });

      res.json({
        success: true,
        data: fleets
      });
    } catch (error) {
      logger.error('Get fleet list error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch fleet list'
      });
    }
  }

  // Additional helper methods for load creation
  static async checkContaractual(req, res) {
    try {
      res.json({
        success: true,
        message: 'Check contractual functionality to be implemented'
      });
    } catch (error) {
      logger.error('Check contractual error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check contractual'
      });
    }
  }

  static async getLoadingPoint(req, res) {
    try {
      res.json({
        success: true,
        message: 'Get loading point functionality to be implemented'
      });
    } catch (error) {
      logger.error('Get loading point error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get loading point'
      });
    }
  }

  static async truckweigt(req, res) {
    try {
      res.json({
        success: true,
        message: 'Truck weight functionality to be implemented'
      });
    } catch (error) {
      logger.error('Truck weight error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get truck weight'
      });
    }
  }

  static async getBranchListPage(req, res) {
    try {
      const { id } = req.body; // consignee_id from shipper selection

      if (!id) {
        return res.status(400).json({
          status: 0,
          message: 'Consignee ID is required',
          data: []
        });
      }

      logger.info(`Getting branches for consignee ID: ${id}`);

      // First get the enterprise_id from the consignee (user)
      const consignee = await prisma.user.findFirst({
        where: {
          id: parseInt(id),
          userType: 'CONSIGNEE',
          status: 'ACTIVE'
        },
        select: {
          enterpriseId: true,
          name: true
        }
      });

      if (!consignee) {
        logger.warn(`Consignee not found for ID: ${id}`);
        return res.json({
          status: 0,
          message: 'Consignee not found',
          data: []
        });
      }

      logger.info(`Found consignee ${consignee.name} with enterprise ID: ${consignee.enterpriseId}`);

      // Get branches for this enterprise - matches bidding_system exactly
      const branches = await prisma.branch.findMany({
        where: {
          enterpriseId: consignee.enterpriseId,
          status: 1
        },
        select: {
          id: true,
          name: true,
          city: true,
          state: true,
          address: true,
          contactPerson: true,
          contactMobile: true
        },
        orderBy: {
          id: 'desc'
        }
      });

      logger.info(`Found ${branches.length} branches for enterprise ${consignee.enterpriseId}`);

      // Format response exactly like bidding_system
      const formattedBranches = branches.map(branch => ({
        id: branch.id,
        name: branch.name,
        city: branch.city || '',
        state: branch.state || '',
        address: branch.address || '',
        contactPerson: branch.contactPerson || '',
        contactMobile: branch.contactMobile || ''
      }));

      res.json({
        status: 1,
        data: formattedBranches,
        message: formattedBranches.length > 0 ? `Found ${formattedBranches.length} branches` : 'No branches found'
      });

    } catch (error) {
      logger.error('Get branch list page error:', error);
      res.status(500).json({
        status: 0,
        error: 'Failed to get branch list',
        data: []
      });
    }
  }

  static async getBiddingType(req, res) {
    try {
      res.json({
        success: true,
        message: 'Get bidding type functionality to be implemented'
      });
    } catch (error) {
      logger.error('Get bidding type error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get bidding type'
      });
    }
  }

  static async getFixedFloatComment(req, res) {
    try {
      res.json({
        success: true,
        message: 'Get fixed float comment functionality to be implemented'
      });
    } catch (error) {
      logger.error('Get fixed float comment error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get fixed float comment'
      });
    }
  }

  static async getConsigneeAssociationList(req, res) {
    try {
      res.json({
        success: true,
        message: 'Get consignee association list functionality to be implemented'
      });
    } catch (error) {
      logger.error('Get consignee association list error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get consignee association list'
      });
    }
  }

  static async getConsigneePhone(req, res) {
    try {
      res.json({
        success: true,
        message: 'Get consignee phone functionality to be implemented'
      });
    } catch (error) {
      logger.error('Get consignee phone error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get consignee phone'
      });
    }
  }
}

module.exports = AdminLoadController;
