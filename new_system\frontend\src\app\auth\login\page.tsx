'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import { useAuthStore } from '@/store/authStore';
import { TruckIcon, DevicePhoneMobileIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { api } from '@/lib/api';

// SMS OTP Login Schema (Mobile-only login as per legacy system)
const mobileLoginSchema = z.object({
  mobile: z.string()
    .regex(/^[6-9]\d{9}$/, 'Mobile number must be 10 digits starting with 6-9')
    .length(10, 'Mobile number must be exactly 10 digits'),
});

// OTP Verification Schema
const otpVerificationSchema = z.object({
  otp: z.string()
    .length(6, 'OTP must be exactly 6 digits')
    .regex(/^\d{6}$/, 'OTP must contain only numbers'),
});

type MobileLoginData = z.infer<typeof mobileLoginSchema>;
type OtpVerificationData = z.infer<typeof otpVerificationSchema>;

export default function LoginPage() {
  const router = useRouter();
  const { isLoading } = useAuthStore();
  const [step, setStep] = useState<'mobile' | 'otp'>('mobile');
  const [mobile, setMobile] = useState('');
  const [userId, setUserId] = useState<number | null>(null);
  const [otpSent, setOtpSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // Mobile login form
  const mobileForm = useForm<MobileLoginData>({
    resolver: zodResolver(mobileLoginSchema),
  });

  // OTP verification form
  const otpForm = useForm<OtpVerificationData>({
    resolver: zodResolver(otpVerificationSchema),
  });

  // Send OTP
  const onMobileSubmit = async (data: MobileLoginData) => {
    try {
      const response = await api.post('/auth/bypass/get-otp', { login_mobile: data.mobile });

      if (response.data.alert === 'success') {
        setMobile(data.mobile);
        setUserId(response.data.user_id);
        setStep('otp');
        setOtpSent(true);
        setCountdown(120); // 2 minutes countdown

        // Start countdown timer
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        toast.success('OTP sent to your mobile number!');
      } else {
        toast.error(response.data.message || 'Failed to send OTP');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to send OTP');
    }
  };

  // Verify OTP
  const onOtpSubmit = async (data: OtpVerificationData) => {
    try {
      const response = await api.post('/auth/bypass/login-otp', {
        user_id: userId,
        user_otp: data.otp
      });

      if (response.data.alert === 'success') {
        const { token, user } = response.data;

        // Store token and user data
        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(user));

        toast.success('Login successful!');

        // Redirect based on user type - matches bidding_system exactly
        if (response.data.user_type === 'admin/dashboard') {
          router.push('/admin/dashboard');
        } else if (response.data.user_type === 'consignee/dashboard') {
          router.push('/consignee/dashboard');
        } else if (response.data.user_type === 'supply-partner/dashboard') {
          router.push('/supply-partner/dashboard');
        } else {
          router.push('/dashboard');
        }
      } else {
        toast.error(response.data.message || 'Invalid OTP');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'OTP verification failed');
    }
  };

  // Resend OTP
  const resendOtp = async () => {
    if (countdown > 0) return;

    try {
      const response = await api.post('/auth/bypass/get-otp', { login_mobile: mobile });

      if (response.data.alert === 'success') {
        setCountdown(120);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        toast.success('OTP resent successfully!');
      }
    } catch (error: any) {
      toast.error('Failed to resend OTP');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <div className="relative sm:mx-auto sm:w-full sm:max-w-md">
        {/* Logo Section */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-[#0e2d67] to-[#1a4480] flex items-center justify-center shadow-xl">
              <TruckIcon className="h-8 w-8 text-white" />
            </div>
            <div className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-[#e3000f] flex items-center justify-center">
              <span className="text-white font-bold text-xs">EP</span>
            </div>
          </div>
        </div>

        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {step === 'mobile' ? 'Welcome to eParivahan' : 'Verify OTP'}
          </h1>
          <p className="text-gray-600 mb-8">
            {step === 'mobile'
              ? 'Enter your mobile number to receive OTP'
              : `OTP sent to ${mobile}. Enter the 6-digit code.`
            }
          </p>
        </div>
      </div>

      <div className="relative mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8">

          {/* Mobile Number Step */}
          {step === 'mobile' && (
            <form className="space-y-6" onSubmit={mobileForm.handleSubmit(onMobileSubmit)}>
              <div className="space-y-2">
                <label htmlFor="mobile" className="block text-sm font-semibold text-gray-700">
                  <DevicePhoneMobileIcon className="h-5 w-5 inline mr-2" />
                  Mobile Number
                </label>
                <div className="relative">
                  <input
                    {...mobileForm.register('mobile')}
                    type="tel"
                    autoComplete="tel"
                    maxLength={10}
                    className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 ${
                      mobileForm.formState.errors.mobile
                        ? 'border-red-300 focus:border-red-500'
                        : 'border-gray-200 focus:border-[#0e2d67] hover:border-gray-300'
                    } bg-white/50 backdrop-blur-sm text-lg tracking-wider`}
                    placeholder="9876543210"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 text-sm">+91</span>
                  </div>
                </div>
                {mobileForm.formState.errors.mobile && (
                  <p className="text-sm text-red-600 flex items-center mt-1">
                    <span className="mr-1">⚠</span>
                    {mobileForm.formState.errors.mobile.message}
                  </p>
                )}
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2" />
                      Sending OTP...
                    </div>
                  ) : (
                    <span className="flex items-center justify-center">
                      <DevicePhoneMobileIcon className="h-5 w-5 mr-2" />
                      Send OTP
                    </span>
                  )}
                </button>
              </div>
            </form>
          )}

          {/* OTP Verification Step */}
          {step === 'otp' && (
            <form className="space-y-6" onSubmit={otpForm.handleSubmit(onOtpSubmit)}>
              <div className="space-y-2">
                <label htmlFor="otp" className="block text-sm font-semibold text-gray-700">
                  <ShieldCheckIcon className="h-5 w-5 inline mr-2" />
                  Enter OTP
                </label>
                <div className="relative">
                  <input
                    {...otpForm.register('otp')}
                    type="text"
                    maxLength={6}
                    className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 ${
                      otpForm.formState.errors.otp
                        ? 'border-red-300 focus:border-red-500'
                        : 'border-gray-200 focus:border-[#0e2d67] hover:border-gray-300'
                    } bg-white/50 backdrop-blur-sm text-center text-2xl tracking-widest font-mono`}
                    placeholder="000000"
                  />
                </div>
                {otpForm.formState.errors.otp && (
                  <p className="text-sm text-red-600 flex items-center mt-1">
                    <span className="mr-1">⚠</span>
                    {otpForm.formState.errors.otp.message}
                  </p>
                )}
              </div>

              {/* Countdown Timer */}
              {countdown > 0 && (
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Resend OTP in <span className="font-semibold text-[#0e2d67]">{Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}</span>
                  </p>
                </div>
              )}

              <div className="space-y-3">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2" />
                      Verifying...
                    </div>
                  ) : (
                    <span className="flex items-center justify-center">
                      <ShieldCheckIcon className="h-5 w-5 mr-2" />
                      Verify & Login
                    </span>
                  )}
                </button>

                {/* Resend OTP Button */}
                <button
                  type="button"
                  onClick={resendOtp}
                  disabled={countdown > 0}
                  className="w-full border-2 border-gray-200 text-gray-700 font-semibold py-2 px-4 rounded-xl hover:border-[#0e2d67] hover:text-[#0e2d67] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 bg-white/50 backdrop-blur-sm"
                >
                  {countdown > 0 ? 'Please wait...' : 'Resend OTP'}
                </button>

                {/* Back to Mobile Step */}
                <button
                  type="button"
                  onClick={() => {
                    setStep('mobile');
                    setCountdown(0);
                    otpForm.reset();
                  }}
                  className="w-full text-gray-600 font-medium py-2 px-4 rounded-xl hover:text-[#0e2d67] focus:outline-none transition-all duration-200"
                >
                  ← Change Mobile Number
                </button>
              </div>
            </form>
          )}

        </div>
      </div>

      {/* Demo Credentials & Default OTPs */}
      <div className="relative mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-100 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center mb-3">
            <div className="h-8 w-8 rounded-lg bg-blue-500 flex items-center justify-center mr-3">
              <span className="text-white font-bold text-sm">🔑</span>
            </div>
            <h3 className="text-sm font-bold text-blue-900">Demo Login Details</h3>
          </div>
          <div className="space-y-3">
            <div className="bg-white/60 rounded-lg p-3 border border-blue-200">
              <p className="text-xs font-semibold text-blue-800 mb-1">Admin User</p>
              <p className="text-sm text-blue-900 font-mono">📱 8240301895</p>
            </div>
            <div className="bg-white/60 rounded-lg p-3 border border-blue-200">
              <p className="text-xs font-semibold text-blue-800 mb-1">Consignee (Load Poster)</p>
              <p className="text-sm text-blue-900 font-mono">📱 9874896900</p>
            </div>
            <div className="bg-white/60 rounded-lg p-3 border border-blue-200">
              <p className="text-xs font-semibold text-blue-800 mb-1">Fleet Owner (Transporter)</p>
              <p className="text-sm text-blue-900 font-mono">📱 8181816477</p>
            </div>
            <div className="bg-white/60 rounded-lg p-3 border border-blue-200">
              <p className="text-xs font-semibold text-blue-800 mb-1">Fleet Owner (Transporter)</p>
              <p className="text-sm text-blue-900 font-mono">📱 9051720133</p>
            </div>
          </div>

          {/* Default OTPs */}
          <div className="mt-4 pt-4 border-t border-blue-200">
            <p className="text-xs font-semibold text-blue-800 mb-2">Default OTPs (Admin Access)</p>
            <div className="grid grid-cols-5 gap-2">
              {['123456', '111111', '000000', '999999', '555555'].map((otp) => (
                <div key={otp} className="bg-white/80 rounded-lg p-2 text-center border border-blue-200">
                  <p className="text-xs font-mono text-blue-900">{otp}</p>
                </div>
              ))}
            </div>
            <p className="text-xs text-blue-600 mt-2">
              These OTPs work for any mobile number for testing purposes.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
