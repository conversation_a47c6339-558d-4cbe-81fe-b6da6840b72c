const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const { authMiddleware } = require('../middleware/auth');
const smsOtpController = require('../controllers/smsOtpController');

const router = express.Router();
const prisma = new PrismaClient();

// Generate JWT tokens
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '1h' }
  );

  const refreshToken = jwt.sign(
    { userId },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );

  return { accessToken, refreshToken };
};

// ============================================================================
// SMS OTP AUTHENTICATION ROUTES (NEW IMPLEMENTATION)
// ============================================================================

// @route   POST /api/auth/mobile-login
// @desc    Mobile login with OTP generation - DISABLED (using bidding_system compatible routes)
// @access  Public
// router.post('/mobile-login', [
//   body('mobile').isMobilePhone('en-IN').withMessage('Please provide a valid Indian mobile number')
// ], smsOtpController.mobileLogin);

// @route   POST /api/auth/verify-otp
// @desc    Verify OTP and complete login - DISABLED (using bidding_system compatible routes)
// @access  Public
// router.post('/verify-otp', [
//   body('mobile').isMobilePhone('en-IN').withMessage('Please provide a valid mobile number'),
//   body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits')
// ], smsOtpController.verifyOtp);

// @route   POST /api/auth/reset-otp
// @desc    Admin reset OTP for user
// @access  Protected (Admin only)
router.post('/reset-otp', authMiddleware, smsOtpController.resetOtp);

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Protected
router.get('/me', authMiddleware, smsOtpController.getCurrentUser);

// ============================================================================
// LEGACY AUTHENTICATION ROUTES (KEEP FOR BACKWARD COMPATIBILITY)
// ============================================================================

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', [
  body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').optional().isEmail().withMessage('Please provide a valid email'),
  body('mobile').isMobilePhone().withMessage('Please provide a valid mobile number'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('enterpriseId').isInt().withMessage('Enterprise ID is required'),
  body('userType').isIn(['CONSIGNEE', 'FLEET', 'DRIVER']).withMessage('Invalid user type')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { name, email, mobile, password, enterpriseId, userType } = req.body;

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { mobile },
          ...(email ? [{ email }] : [])
        ]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this mobile number or email already exists'
      });
    }

    // Check if enterprise exists
    const enterprise = await prisma.enterprise.findUnique({
      where: { id: enterpriseId }
    });

    if (!enterprise) {
      return res.status(400).json({
        success: false,
        error: 'Enterprise not found'
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        mobile,
        password: hashedPassword,
        enterpriseId,
        userType,
        status: 'PENDING' // Requires admin approval
      },
      include: {
        enterprise: true
      }
    });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Update user with refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: { refreshToken }
    });

    logger.info(`New user registered: ${user.mobile}`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Awaiting admin approval.',
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          mobile: user.mobile,
          userType: user.userType,
          status: user.status,
          enterprise: user.enterprise
        },
        accessToken,
        refreshToken
      }
    });

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during registration'
    });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', [
  body('mobile').isMobilePhone().withMessage('Please provide a valid mobile number'),
  body('password').exists().withMessage('Password is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { mobile, password } = req.body;

    // Find user
    const user = await prisma.user.findUnique({
      where: { mobile },
      include: {
        enterprise: true
      }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      return res.status(401).json({
        success: false,
        error: 'Account is not active. Please contact administrator.'
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Note: refreshToken and lastSeenAt fields don't exist in current schema
    // Skip updating these fields for now

    logger.info(`User logged in: ${user.mobile}`);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          mobile: user.mobile,
          userType: user.userType,
          enterpriseId: user.enterpriseId,
          enterprise: user.enterprise,
          isAdmin: user.isAdmin,
          isManager: user.isManager,
          isDriver: user.isDriver
        },
        accessToken,
        refreshToken
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during login'
    });
  }
});

// @route   POST /api/auth/refresh
// @desc    Refresh access token
// @access  Public
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        error: 'Refresh token is required'
      });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

    // Find user using raw SQL (refresh token is stored in JWT, not database)
    const users = await prisma.$queryRaw`
      SELECT
        u.id,
        u.name,
        u.mobile,
        u.user_type,
        u.status,
        u.enterprise_id,
        e.organisation_name,
        e.is_verified
      FROM eparivahan_live_db.users u
      LEFT JOIN eparivahan_live_db.enterprises e ON u.enterprise_id = e.id
      WHERE u.id = ${decoded.userId} AND u.status = 'ACTIVE'
      LIMIT 1
    `;

    const user = users.length > 0 ? users[0] : null;

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid refresh token'
      });
    }

    // Generate new tokens
    const tokens = generateTokens(user.id);

    // Update user with new refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: { refreshToken: tokens.refreshToken }
    });

    res.json({
      success: true,
      data: tokens
    });

  } catch (error) {
    logger.error('Token refresh error:', error);
    res.status(401).json({
      success: false,
      error: 'Invalid refresh token'
    });
  }
});

// @route   POST /api/auth/logout
// @desc    Logout user
// @access  Private
router.post('/logout', authMiddleware, async (req, res) => {
  try {
    // Clear refresh token
    await prisma.user.update({
      where: { id: req.user.id },
      data: { refreshToken: null }
    });

    logger.info(`User logged out: ${req.user.mobile}`);

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during logout'
    });
  }
});

// Duplicate route removed - using smsOtpController.getCurrentUser instead

// ============================================================================
// BIDDING_SYSTEM COMPATIBLE AUTHENTICATION ROUTES
// ============================================================================

const AuthController = require('../controllers/auth/AuthController');

// @route   GET /api/auth/bypass/login
// @desc    Login page - matches bidding_system /bypass/login
// @access  Public
router.get('/bypass/login', AuthController.index);

// @route   POST /api/auth/bypass/get-otp
// @desc    Get OTP for mobile login - matches bidding_system /bypass/get-otp
// @access  Public
router.post('/bypass/get-otp', AuthController.login);

// @route   POST /api/auth/bypass/login-otp
// @desc    Verify OTP and login - matches bidding_system /bypass/login-otp
// @access  Public
router.post('/bypass/login-otp', AuthController.verifyOtp);

// @route   ANY /api/auth/bypass/user_logout
// @desc    User logout - matches bidding_system /bypass/user_logout
// @access  Private
router.all('/bypass/user_logout', authMiddleware, AuthController.logout);

module.exports = router;
