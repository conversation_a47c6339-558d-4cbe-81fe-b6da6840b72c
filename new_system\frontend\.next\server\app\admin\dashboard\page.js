/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/dashboard/page";
exports.ids = ["app/admin/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/dashboard/page.tsx */ \"(rsc)/./src/app/admin/dashboard/page.tsx\")), \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/dashboard/page\",\n        pathname: \"/admin/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhZG1pbiUyRmRhc2hib2FyZCUyRnBhZ2UmcGFnZT0lMkZhZG1pbiUyRmRhc2hib2FyZCUyRnBhZ2UmYXBwUGF0aHM9JTJGYWRtaW4lMkZkYXNoYm9hcmQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYWRtaW4lMkZkYXNoYm9hcmQlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q2VwYXJpdmFoYW4lNUNhdWdtZW50X2p1bmVfMjZfMjYlNUNuZXdfc3lzdGVtJTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNlcGFyaXZhaGFuJTVDYXVnbWVudF9qdW5lXzI2XzI2JTVDbmV3X3N5c3RlbSU1Q2Zyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixnTEFBbUk7QUFDMUo7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBbUg7QUFDNUksb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL3VuaWZpZWQtdHJhbnNwb3J0LWZyb250ZW5kLz9lMzIxIl0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2FkbWluJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZGFzaGJvYXJkJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcZXBhcml2YWhhblxcXFxhdWdtZW50X2p1bmVfMjZfMjZcXFxcbmV3X3N5c3RlbVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSwgXCJDOlxcXFxlcGFyaXZhaGFuXFxcXGF1Z21lbnRfanVuZV8yNl8yNlxcXFxuZXdfc3lzdGVtXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW5cXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGVwYXJpdmFoYW5cXFxcYXVnbWVudF9qdW5lXzI2XzI2XFxcXG5ld19zeXN0ZW1cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkM6XFxcXGVwYXJpdmFoYW5cXFxcYXVnbWVudF9qdW5lXzI2XzI2XFxcXG5ld19zeXN0ZW1cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxlcGFyaXZhaGFuXFxcXGF1Z21lbnRfanVuZV8yNl8yNlxcXFxuZXdfc3lzdGVtXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW5cXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYWRtaW4vZGFzaGJvYXJkL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvYWRtaW4vZGFzaGJvYXJkL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FkbWluL2Rhc2hib2FyZFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CDashboardLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CDashboardLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/DashboardLayout.tsx */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ceparivahan%5C%5Caugment_june_26_26%5C%5Cnew_system%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CDashboardLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var _store_socketStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/socketStore */ \"(ssr)/./src/store/socketStore.ts\");\n/* harmony import */ var _components_providers_SocketProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/SocketProvider */ \"(ssr)/./src/components/providers/SocketProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const { initializeAuth } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect } = (0,_store_socketStore__WEBPACK_IMPORTED_MODULE_3__.useSocketStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize authentication state from localStorage\n        initializeAuth();\n    }, [\n        initializeAuth\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SocketProvider__WEBPACK_IMPORTED_MODULE_4__.SocketProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRWtDO0FBQ2U7QUFDSTtBQUNrQjtBQUVoRSxTQUFTSSxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUsTUFBTSxFQUFFQyxjQUFjLEVBQUUsR0FBR0wsOERBQVlBO0lBQ3ZDLE1BQU0sRUFBRU0sT0FBTyxFQUFFLEdBQUdMLGtFQUFjQTtJQUVsQ0YsZ0RBQVNBLENBQUM7UUFDUixvREFBb0Q7UUFDcERNO0lBQ0YsR0FBRztRQUFDQTtLQUFlO0lBRW5CLHFCQUNFLDhEQUFDSCxnRkFBY0E7a0JBQ1pFOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL3VuaWZpZWQtdHJhbnNwb3J0LWZyb250ZW5kLy4vc3JjL2FwcC9wcm92aWRlcnMudHN4PzkzMjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoU3RvcmUgfSBmcm9tICdAL3N0b3JlL2F1dGhTdG9yZSc7XG5pbXBvcnQgeyB1c2VTb2NrZXRTdG9yZSB9IGZyb20gJ0Avc3RvcmUvc29ja2V0U3RvcmUnO1xuaW1wb3J0IHsgU29ja2V0UHJvdmlkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvcHJvdmlkZXJzL1NvY2tldFByb3ZpZGVyJztcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IHsgaW5pdGlhbGl6ZUF1dGggfSA9IHVzZUF1dGhTdG9yZSgpO1xuICBjb25zdCB7IGNvbm5lY3QgfSA9IHVzZVNvY2tldFN0b3JlKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsaXplIGF1dGhlbnRpY2F0aW9uIHN0YXRlIGZyb20gbG9jYWxTdG9yYWdlXG4gICAgaW5pdGlhbGl6ZUF1dGgoKTtcbiAgfSwgW2luaXRpYWxpemVBdXRoXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8U29ja2V0UHJvdmlkZXI+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9Tb2NrZXRQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBdXRoU3RvcmUiLCJ1c2VTb2NrZXRTdG9yZSIsIlNvY2tldFByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJpbml0aWFsaXplQXV0aCIsImNvbm5lY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuthPersistence__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuthPersistence */ \"(ssr)/./src/hooks/useAuthPersistence.ts\");\n/* harmony import */ var _Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Navigation */ \"(ssr)/./src/components/layout/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isInitialized, isAuthenticated, user } = (0,_hooks_useAuthPersistence__WEBPACK_IMPORTED_MODULE_3__.useAuthPersistence)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only redirect after auth is initialized\n        if (isInitialized && !isAuthenticated && !pathname.startsWith(\"/auth\")) {\n            router.push(\"/auth/login\");\n        }\n    }, [\n        isInitialized,\n        isAuthenticated,\n        router,\n        pathname\n    ]);\n    // Role-based route protection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && isAuthenticated && user) {\n            const userRole = user.userType;\n            // Define role-based route access\n            const roleRouteMap = {\n                ADMIN: [\n                    \"/admin\",\n                    \"/dashboard\",\n                    \"/tracking\"\n                ],\n                CONSIGNEE: [\n                    \"/consignee\",\n                    \"/dashboard\",\n                    \"/tracking\"\n                ],\n                FLEET: [\n                    \"/fleet\",\n                    \"/dashboard\",\n                    \"/tracking\"\n                ]\n            };\n            // Check if current path is allowed for user role\n            const allowedPaths = roleRouteMap[userRole] || [];\n            const isPathAllowed = allowedPaths.some((path)=>pathname.startsWith(path)) || pathname === \"/\" || pathname.startsWith(\"/auth\") || pathname.startsWith(\"/profile\") || pathname.startsWith(\"/settings\");\n            if (!isPathAllowed) {\n                // Redirect to appropriate dashboard based on role\n                const defaultPaths = {\n                    ADMIN: \"/admin/dashboard\",\n                    CONSIGNEE: \"/consignee/dashboard\",\n                    FLEET: \"/fleet/dashboard\"\n                };\n                const redirectPath = defaultPaths[userRole] || \"/dashboard\";\n                router.push(redirectPath);\n            }\n        }\n    }, [\n        isInitialized,\n        isAuthenticated,\n        user,\n        pathname,\n        router\n    ]);\n    // Don't render layout for auth pages\n    if (pathname.startsWith(\"/auth\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Show loading if auth is not initialized or not authenticated\n    if (!isInitialized || !isAuthenticated && !pathname.startsWith(\"/auth\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-950\"\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-72\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,Bars3Icon,BellIcon,BuildingOfficeIcon,ChartBarIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,Cog6ToothIcon,DocumentTextIcon,EnvelopeIcon,ExclamationTriangleIcon,HomeIcon,MapIcon,PhoneIcon,ShoppingCartIcon,TruckIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Enhanced navigation structure with all menu options from both systems\nconst adminNavigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Load Management\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        children: [\n            {\n                name: \"All Loads\",\n                href: \"/admin/loads\"\n            },\n            {\n                name: \"Create Load\",\n                href: \"/admin/loads/create\"\n            },\n            {\n                name: \"Pending Loads\",\n                href: \"/admin/loads?status=pending\"\n            },\n            {\n                name: \"Active Loads\",\n                href: \"/admin/loads?status=active\"\n            },\n            {\n                name: \"Completed Loads\",\n                href: \"/admin/loads?status=completed\"\n            }\n        ]\n    },\n    {\n        name: \"Enterprise Management\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        children: [\n            {\n                name: \"All Enterprises\",\n                href: \"/admin/enterprises\"\n            },\n            {\n                name: \"Add Enterprise\",\n                href: \"/admin/enterprises/create\"\n            },\n            {\n                name: \"Pending Verification\",\n                href: \"/admin/enterprises?status=new\"\n            },\n            {\n                name: \"Active Enterprises\",\n                href: \"/admin/enterprises?status=active\"\n            }\n        ]\n    },\n    {\n        name: \"User Management\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        children: [\n            {\n                name: \"All Users\",\n                href: \"/admin/users\"\n            },\n            {\n                name: \"Add User\",\n                href: \"/admin/users/create\"\n            },\n            {\n                name: \"Admin Users\",\n                href: \"/admin/users?type=admin\"\n            },\n            {\n                name: \"Consignee Users\",\n                href: \"/admin/users?type=consignee\"\n            },\n            {\n                name: \"Fleet Users\",\n                href: \"/admin/users?type=fleet\"\n            }\n        ]\n    },\n    {\n        name: \"Vehicle Management\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        children: [\n            {\n                name: \"All Vehicles\",\n                href: \"/admin/vehicles\"\n            },\n            {\n                name: \"Add Vehicle\",\n                href: \"/admin/vehicles/create\"\n            },\n            {\n                name: \"Pending Approval\",\n                href: \"/admin/vehicles?status=pending\"\n            },\n            {\n                name: \"Active Vehicles\",\n                href: \"/admin/vehicles?status=active\"\n            }\n        ]\n    },\n    {\n        name: \"Most Travelled Routes\",\n        href: \"/routes/popular\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Enterprise Users\",\n        href: \"/enterprise/users\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Enterprise Branches\",\n        href: \"/enterprise/branches\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"My Association\",\n        href: \"/association\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Admin Reports\",\n        href: \"/reports/admin\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Rate Trend Reports\",\n        href: \"/reports/rate-trends\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Bank Guarantee\",\n        href: \"/bank/guarantee\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        name: \"Transporter Login Details\",\n        href: \"/transporter/login-details\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        name: \"Reset Default OTP\",\n        href: \"/otp/reset\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        name: \"Track SMS\",\n        href: \"/sms/track\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    }\n];\nconst consigneeNavigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Tracking\",\n        href: \"/tracking/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        children: [\n            {\n                name: \"Tracking Dashboard\",\n                href: \"/tracking/dashboard\"\n            },\n            {\n                name: \"Vehicle Tracking\",\n                children: [\n                    {\n                        name: \"Real-time Tracking\",\n                        href: \"/tracking/vehicles/realtime\"\n                    },\n                    {\n                        name: \"Vehicle Status\",\n                        href: \"/tracking/vehicles/status\"\n                    },\n                    {\n                        name: \"Route Monitoring\",\n                        href: \"/tracking/vehicles/routes\"\n                    },\n                    {\n                        name: \"Driver Tracking\",\n                        href: \"/tracking/vehicles/drivers\"\n                    }\n                ]\n            },\n            {\n                name: \"Shipment Tracking\",\n                children: [\n                    {\n                        name: \"Active Shipments\",\n                        href: \"/tracking/shipments/active\"\n                    },\n                    {\n                        name: \"Delivery Status\",\n                        href: \"/tracking/shipments/delivery\"\n                    },\n                    {\n                        name: \"Proof of Delivery\",\n                        href: \"/tracking/shipments/pod\"\n                    },\n                    {\n                        name: \"Transit Updates\",\n                        href: \"/tracking/shipments/transit\"\n                    }\n                ]\n            },\n            {\n                name: \"Tracking Analytics\",\n                children: [\n                    {\n                        name: \"Performance Metrics\",\n                        href: \"/tracking/analytics/performance\"\n                    },\n                    {\n                        name: \"Route Analytics\",\n                        href: \"/tracking/analytics/routes\"\n                    },\n                    {\n                        name: \"Delivery Reports\",\n                        href: \"/tracking/analytics/delivery\"\n                    },\n                    {\n                        name: \"Exception Reports\",\n                        href: \"/tracking/analytics/exceptions\"\n                    }\n                ]\n            },\n            {\n                name: \"Alerts & Notifications\",\n                children: [\n                    {\n                        name: \"Tracking Alerts\",\n                        href: \"/tracking/alerts/tracking\"\n                    },\n                    {\n                        name: \"Delay Notifications\",\n                        href: \"/tracking/alerts/delays\"\n                    },\n                    {\n                        name: \"Route Deviations\",\n                        href: \"/tracking/alerts/deviations\"\n                    },\n                    {\n                        name: \"Delivery Confirmations\",\n                        href: \"/tracking/alerts/confirmations\"\n                    }\n                ]\n            },\n            {\n                name: \"Customer Portal\",\n                children: [\n                    {\n                        name: \"Customer Tracking\",\n                        href: \"/tracking/customer/portal\"\n                    },\n                    {\n                        name: \"Delivery Consent\",\n                        href: \"/tracking/customer/consent\"\n                    },\n                    {\n                        name: \"Feedback System\",\n                        href: \"/tracking/customer/feedback\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        name: \"Create Load\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        children: [\n            {\n                name: \"Single Load (Spot)\",\n                href: \"/consignee/loads/create/spot\"\n            },\n            {\n                name: \"Single Load (Contractual)\",\n                href: \"/consignee/loads/create/contractual\"\n            },\n            {\n                name: \"Multiple Load\",\n                href: \"/consignee/loads/create/multiple\"\n            }\n        ]\n    },\n    {\n        name: \"Upcoming Loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        children: [\n            {\n                name: \"Upcoming Loads (Spot)\",\n                href: \"/consignee/loads/upcoming/spot\"\n            },\n            {\n                name: \"Upcoming Loads (Contractual)\",\n                href: \"/consignee/loads/upcoming/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Pending Loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        children: [\n            {\n                name: \"Pending Loads (Spot)\",\n                href: \"/consignee/loads/pending/spot\"\n            },\n            {\n                name: \"Pending Loads (Contractual)\",\n                href: \"/consignee/loads/pending/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Partially Confirmed Loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        children: [\n            {\n                name: \"Partially Confirmed (Spot)\",\n                href: \"/loads/partial/spot\"\n            },\n            {\n                name: \"Partially Confirmed (Contractual)\",\n                href: \"/loads/partial/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Confirmed Loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        children: [\n            {\n                name: \"Confirmed Loads (Spot)\",\n                href: \"/consignee/loads/confirmed/spot\"\n            },\n            {\n                name: \"Confirmed Loads (Contractual)\",\n                href: \"/consignee/loads/confirmed/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Manage Users\",\n        href: \"/consignee/users\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Manage Branches\",\n        href: \"/consignee/branches\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Enterprise Profile\",\n        href: \"/consignee/enterprise/profile\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Contracts\",\n        href: \"/consignee/contracts\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Reports\",\n        href: \"/consignee/reports\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nconst fleetNavigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Tracking\",\n        href: \"/tracking/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        children: [\n            {\n                name: \"Tracking Dashboard\",\n                href: \"/tracking/dashboard\"\n            },\n            {\n                name: \"Fleet Tracking\",\n                children: [\n                    {\n                        name: \"Fleet Overview\",\n                        href: \"/tracking/fleet/overview\"\n                    },\n                    {\n                        name: \"Vehicle Locations\",\n                        href: \"/tracking/fleet/locations\"\n                    },\n                    {\n                        name: \"Driver Status\",\n                        href: \"/tracking/fleet/drivers\"\n                    },\n                    {\n                        name: \"Route Optimization\",\n                        href: \"/tracking/fleet/optimization\"\n                    }\n                ]\n            },\n            {\n                name: \"Performance Tracking\",\n                children: [\n                    {\n                        name: \"Fleet Performance\",\n                        href: \"/tracking/performance/fleet\"\n                    },\n                    {\n                        name: \"Driver Performance\",\n                        href: \"/tracking/performance/drivers\"\n                    },\n                    {\n                        name: \"Vehicle Utilization\",\n                        href: \"/tracking/performance/utilization\"\n                    },\n                    {\n                        name: \"Fuel Efficiency\",\n                        href: \"/tracking/performance/fuel\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        name: \"Manage Vehicles\",\n        href: \"/fleet/vehicles\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Manage Users\",\n        href: \"/fleet/users\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Market Place\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        children: [\n            {\n                name: \"Market Place (Spot)\",\n                href: \"/fleet/loads/acquire/spot\"\n            },\n            {\n                name: \"Market Place (Contractual)\",\n                href: \"/fleet/loads/acquire/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Active Loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        children: [\n            {\n                name: \"Active Loads (Spot)\",\n                href: \"/fleet/loads/active/spot\"\n            },\n            {\n                name: \"Active Loads (Contractual)\",\n                href: \"/fleet/loads/active/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Pending Loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        children: [\n            {\n                name: \"Pending Loads (Spot)\",\n                href: \"/fleet/loads/pending/spot\"\n            },\n            {\n                name: \"Pending Loads (Contractual)\",\n                href: \"/fleet/loads/pending/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Confirm Loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        children: [\n            {\n                name: \"Confirm Loads (Spot)\",\n                href: \"/fleet/loads/confirmed/spot\"\n            },\n            {\n                name: \"Confirm Loads (Contractual)\",\n                href: \"/fleet/loads/confirmed/contractual\"\n            }\n        ]\n    },\n    {\n        name: \"Close Loads\",\n        href: \"/fleet/loads/closed\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"My Loads\",\n        href: \"/fleet/my-loads\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Enterprise Profile\",\n        href: \"/fleet/enterprise/profile\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Contracts\",\n        href: \"/fleet/contracts\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Reports\",\n        href: \"/fleet/reports\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nconst driverNavigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"My Tracking\",\n        href: \"/tracking/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        children: [\n            {\n                name: \"Current Location\",\n                href: \"/tracking/driver/location\"\n            },\n            {\n                name: \"Route Progress\",\n                href: \"/tracking/driver/progress\"\n            },\n            {\n                name: \"Delivery Updates\",\n                href: \"/tracking/driver/delivery\"\n            },\n            {\n                name: \"Trip History\",\n                href: \"/tracking/driver/history\"\n            }\n        ]\n    },\n    {\n        name: \"My Loads\",\n        href: \"/loads/my\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Notifications\",\n        href: \"/notifications\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Navigation() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, logout } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleLogout = ()=>{\n        logout();\n        router.push(\"/auth/login\");\n    };\n    const toggleExpanded = (itemName)=>{\n        setExpandedItems((prev)=>prev.includes(itemName) ? prev.filter((name)=>name !== itemName) : [\n                ...prev,\n                itemName\n            ]);\n    };\n    const getNavigationForUser = ()=>{\n        const userType = user?.userType?.toUpperCase();\n        switch(userType){\n            case \"ADMIN\":\n                return adminNavigation;\n            case \"CONSIGNEE\":\n                return consigneeNavigation;\n            case \"FLEET\":\n                return fleetNavigation;\n            case \"DRIVER\":\n                return driverNavigation;\n            default:\n                // If no specific user type, show basic navigation based on roles\n                if (user?.isAdmin) return adminNavigation;\n                if (user?.isManager) return consigneeNavigation;\n                if (user?.isDriver) return driverNavigation;\n                return consigneeNavigation; // Default fallback\n        }\n    };\n    const filteredNavigation = getNavigationForUser();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-white border-r border-gray-200 px-6 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 shrink-0 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg gradient-primary flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"UTP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 text-xl font-bold text-gray-900\",\n                                    children: \"Transport Platform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-1 flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                role: \"list\",\n                                className: \"flex flex-1 flex-col gap-y-7\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            role: \"list\",\n                                            className: \"-mx-2 space-y-1\",\n                                            children: filteredNavigation.map((item)=>{\n                                                const isActive = pathname === item.href;\n                                                const isExpanded = expandedItems.includes(item.name);\n                                                const hasChildren = item.children && item.children.length > 0;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>toggleExpanded(item.name),\n                                                                className: `nav-link nav-link-inactive w-full text-left flex items-center justify-between`,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                className: \"h-6 w-6 shrink-0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            item.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"ml-6 mt-1 space-y-1\",\n                                                                children: item.children.map((child)=>{\n                                                                    const childIsActive = pathname === child.href;\n                                                                    const childHasChildren = child.children && child.children.length > 0;\n                                                                    const childIsExpanded = expandedItems.includes(child.name);\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: childHasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>toggleExpanded(child.name),\n                                                                                    className: `nav-link nav-link-inactive w-full text-left text-sm flex items-center justify-between`,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: child.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 384,\n                                                                                            columnNumber: 45\n                                                                                        }, this),\n                                                                                        childIsExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 386,\n                                                                                            columnNumber: 47\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 388,\n                                                                                            columnNumber: 47\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                    lineNumber: 380,\n                                                                                    columnNumber: 43\n                                                                                }, this),\n                                                                                childIsExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                    className: \"ml-4 mt-1 space-y-1\",\n                                                                                    children: child.children.map((grandchild)=>{\n                                                                                        const grandchildIsActive = pathname === grandchild.href;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                                href: grandchild.href,\n                                                                                                className: `nav-link text-xs ${grandchildIsActive ? \"nav-link-active\" : \"nav-link-inactive\"}`,\n                                                                                                children: grandchild.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                lineNumber: 397,\n                                                                                                columnNumber: 53\n                                                                                            }, this)\n                                                                                        }, grandchild.name, false, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 396,\n                                                                                            columnNumber: 51\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                    lineNumber: 392,\n                                                                                    columnNumber: 45\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            href: child.href,\n                                                                            className: `nav-link text-sm ${childIsActive ? \"nav-link-active\" : \"nav-link-inactive\"}`,\n                                                                            children: child.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 41\n                                                                        }, this)\n                                                                    }, child.name, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 37\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: item.href,\n                                                        className: `nav-link ${isActive ? \"nav-link-active\" : \"nav-link-inactive\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"h-6 w-6 shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            item.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, item.name, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"mt-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-200 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center px-3 py-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-8 rounded-full bg-primary-950 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-medium text-sm\",\n                                                                children: user?.name?.charAt(0).toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3 flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                    children: user?.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 truncate\",\n                                                                    children: user?.enterprise?.organisationName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: \"/profile\",\n                                                                className: \"nav-link nav-link-inactive\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Profile\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: \"/settings\",\n                                                                className: \"nav-link nav-link-inactive\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Settings\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleLogout,\n                                                                className: \"nav-link nav-link-inactive w-full text-left\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Sign out\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 p-2.5 text-gray-700 lg:hidden\",\n                                onClick: ()=>setMobileMenuOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-6 rounded gradient-primary flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xs\",\n                                                children: \"UTP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: \"Transport Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this),\n                    mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-50 lg:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-gray-900/80\",\n                                onClick: ()=>setMobileMenuOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mr-16 flex w-full max-w-xs flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-full top-0 flex w-16 justify-center pt-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"-m-2.5 p-2.5\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-16 shrink-0 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-8 rounded-lg gradient-primary flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-sm\",\n                                                                children: \"UTP\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-xl font-bold text-gray-900\",\n                                                            children: \"Transport Platform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                    className: \"flex flex-1 flex-col\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"flex flex-1 flex-col gap-y-7\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                role: \"list\",\n                                                                className: \"-mx-2 space-y-1\",\n                                                                children: filteredNavigation.map((item)=>{\n                                                                    const isActive = pathname === item.href;\n                                                                    const isExpanded = expandedItems.includes(item.name);\n                                                                    const hasChildren = item.children && item.children.length > 0;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>toggleExpanded(item.name),\n                                                                                    className: `nav-link nav-link-inactive w-full text-left flex items-center justify-between`,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                                    className: \"h-6 w-6 shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                    lineNumber: 562,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                item.name\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 561,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 566,\n                                                                                            columnNumber: 41\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 568,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                    className: \"ml-6 mt-1 space-y-1\",\n                                                                                    children: item.children.map((child)=>{\n                                                                                        const childIsActive = pathname === child.href;\n                                                                                        const childHasChildren = child.children && child.children.length > 0;\n                                                                                        const childIsExpanded = expandedItems.includes(child.name);\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            children: childHasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                        onClick: ()=>toggleExpanded(child.name),\n                                                                                                        className: `nav-link nav-link-inactive w-full text-left text-sm flex items-center justify-between`,\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: child.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                                lineNumber: 586,\n                                                                                                                columnNumber: 53\n                                                                                                            }, this),\n                                                                                                            childIsExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                                                className: \"h-3 w-3\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                                lineNumber: 588,\n                                                                                                                columnNumber: 55\n                                                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_Bars3Icon_BellIcon_BuildingOfficeIcon_ChartBarIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_Cog6ToothIcon_DocumentTextIcon_EnvelopeIcon_ExclamationTriangleIcon_HomeIcon_MapIcon_PhoneIcon_ShoppingCartIcon_TruckIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                                className: \"h-3 w-3\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                                lineNumber: 590,\n                                                                                                                columnNumber: 55\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                        lineNumber: 582,\n                                                                                                        columnNumber: 51\n                                                                                                    }, this),\n                                                                                                    childIsExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                        className: \"ml-4 mt-1 space-y-1\",\n                                                                                                        children: child.children.map((grandchild)=>{\n                                                                                                            const grandchildIsActive = pathname === grandchild.href;\n                                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                                                    href: grandchild.href,\n                                                                                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                                                                                    className: `nav-link text-xs ${grandchildIsActive ? \"nav-link-active\" : \"nav-link-inactive\"}`,\n                                                                                                                    children: grandchild.name\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                                    lineNumber: 599,\n                                                                                                                    columnNumber: 61\n                                                                                                                }, this)\n                                                                                                            }, grandchild.name, false, {\n                                                                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                                lineNumber: 598,\n                                                                                                                columnNumber: 59\n                                                                                                            }, this);\n                                                                                                        })\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                        lineNumber: 594,\n                                                                                                        columnNumber: 53\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                                href: child.href,\n                                                                                                onClick: ()=>setMobileMenuOpen(false),\n                                                                                                className: `nav-link text-sm ${childIsActive ? \"nav-link-active\" : \"nav-link-inactive\"}`,\n                                                                                                children: child.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                lineNumber: 615,\n                                                                                                columnNumber: 49\n                                                                                            }, this)\n                                                                                        }, child.name, false, {\n                                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                            lineNumber: 579,\n                                                                                            columnNumber: 45\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                    lineNumber: 572,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            href: item.href,\n                                                                            onClick: ()=>setMobileMenuOpen(false),\n                                                                            className: `nav-link ${isActive ? \"nav-link-active\" : \"nav-link-inactive\"}`,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                    className: \"h-6 w-6 shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                    lineNumber: 639,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                item.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 632,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, item.name, false, {\n                                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SocketProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/SocketProvider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var _store_socketStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/socketStore */ \"(ssr)/./src/store/socketStore.ts\");\n/* __next_internal_client_entry_do_not_use__ SocketProvider auto */ \n\n\n\nfunction SocketProvider({ children }) {\n    const { isAuthenticated, accessToken, user } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { connect, disconnect, joinRoom } = (0,_store_socketStore__WEBPACK_IMPORTED_MODULE_3__.useSocketStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated && accessToken) {\n            // Connect to socket with authentication\n            connect(accessToken);\n            // Join user's personal room for notifications\n            if (user?.id) {\n                joinRoom(`user-${user.id}`);\n            }\n        } else {\n            // Disconnect when not authenticated\n            disconnect();\n        }\n        // Cleanup on unmount\n        return ()=>{\n            disconnect();\n        };\n    }, [\n        isAuthenticated,\n        accessToken,\n        user?.id,\n        connect,\n        disconnect,\n        joinRoom\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SocketProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuthPersistence.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useAuthPersistence.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthPersistence: () => (/* binding */ useAuthPersistence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAuthPersistence auto */ \n\n\nfunction useAuthPersistence() {\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { initializeAuth, isAuthenticated, user } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                // Check if we have tokens in cookies\n                const accessToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"accessToken\");\n                const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"refreshToken\");\n                if (accessToken && refreshToken) {\n                    // Initialize auth from cookies\n                    await initializeAuth();\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize auth:\", error);\n            } finally{\n                setIsInitialized(true);\n            }\n        };\n        initAuth();\n    }, [\n        initializeAuth\n    ]);\n    return {\n        isInitialized,\n        isAuthenticated,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuthPersistence.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   apiHelpers: () => (/* binding */ apiHelpers),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   consigneeAPI: () => (/* binding */ consigneeAPI),\n/* harmony export */   dashboardAPI: () => (/* binding */ dashboardAPI),\n/* harmony export */   fleetAPI: () => (/* binding */ fleetAPI),\n/* harmony export */   legacyTrackingAPI: () => (/* binding */ legacyTrackingAPI),\n/* harmony export */   loadAPI: () => (/* binding */ loadAPI),\n/* harmony export */   masterDataAPI: () => (/* binding */ masterDataAPI),\n/* harmony export */   quotationAPI: () => (/* binding */ quotationAPI),\n/* harmony export */   trackingAPI: () => (/* binding */ trackingAPI),\n/* harmony export */   vehicleAPI: () => (/* binding */ vehicleAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: \"http://localhost:3001/api\" || 0,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"accessToken\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors and token refresh\napi.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    // Handle 401 errors (unauthorized)\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        // Temporarily disable automatic token refresh due to backend database issues\n        // This prevents console errors while keeping users logged in\n        console.log(\"API call failed with 401, but keeping user logged in (token refresh disabled)\");\n        // Only redirect to login for critical auth endpoints\n        if (originalRequest.url?.includes(\"/auth/me\") || originalRequest.url?.includes(\"/auth/logout\")) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"accessToken\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refreshToken\");\n            if (false) {}\n        }\n        // For other endpoints, just return the error without trying to refresh\n        return Promise.reject(error);\n    }\n    // Handle other errors\n    const errorMessage = error.response?.data?.error || error.message || \"An error occurred\";\n    // Don't show toast for certain errors\n    const silentErrors = [\n        401,\n        403\n    ];\n    if (!silentErrors.includes(error.response?.status)) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n    }\n    return Promise.reject(error);\n});\n// API helper functions\nconst apiHelpers = {\n    // Generic GET request\n    get: (url, config)=>{\n        return api.get(url, config);\n    },\n    // Generic POST request\n    post: (url, data, config)=>{\n        return api.post(url, data, config);\n    },\n    // Generic PUT request\n    put: (url, data, config)=>{\n        return api.put(url, data, config);\n    },\n    // Generic PATCH request\n    patch: (url, data, config)=>{\n        return api.patch(url, data, config);\n    },\n    // Generic DELETE request\n    delete: (url, config)=>{\n        return api.delete(url, config);\n    },\n    // File upload\n    upload: (url, formData, onProgress)=>{\n        return api.post(url, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            },\n            onUploadProgress: (progressEvent)=>{\n                if (onProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onProgress(progress);\n                }\n            }\n        });\n    },\n    // Download file\n    download: (url, filename)=>{\n        return api.get(url, {\n            responseType: \"blob\"\n        }).then((response)=>{\n            const blob = new Blob([\n                response.data\n            ]);\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const link = document.createElement(\"a\");\n            link.href = downloadUrl;\n            link.download = filename || \"download\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(downloadUrl);\n        });\n    }\n};\n// Specific API endpoints\nconst authAPI = {\n    login: (mobile, password)=>api.post(\"/auth/login\", {\n            mobile,\n            password\n        }),\n    register: (userData)=>api.post(\"/auth/register\", userData),\n    logout: ()=>api.post(\"/auth/logout\"),\n    refresh: (refreshToken)=>api.post(\"/auth/refresh-token\", {\n            refreshToken\n        }),\n    me: ()=>api.get(\"/auth/me\"),\n    verifyOtp: (mobile, otp)=>api.post(\"/auth/verify-otp\", {\n            mobile,\n            otp\n        }),\n    resendOtp: (mobile)=>api.post(\"/auth/resend-otp\", {\n            mobile\n        }),\n    forgotPassword: (mobile)=>api.post(\"/auth/forgot-password\", {\n            mobile\n        }),\n    resetPassword: (token, password)=>api.post(\"/auth/reset-password\", {\n            token,\n            password\n        })\n};\nconst loadAPI = {\n    getLoads: (params)=>api.get(\"/loads\", {\n            params\n        }),\n    getLoad: (id)=>api.get(`/loads/${id}`),\n    createLoad: (data)=>api.post(\"/loads\", data),\n    updateLoad: (id, data)=>api.put(`/loads/${id}`, data),\n    deleteLoad: (id)=>api.delete(`/loads/${id}`)\n};\nconst quotationAPI = {\n    getQuotations: (loadId)=>api.get(`/quotations?loadId=${loadId}`),\n    createQuotation: (data)=>api.post(\"/quotations\", data),\n    updateQuotation: (id, data)=>api.put(`/quotations/${id}`, data),\n    acceptQuotation: (id)=>api.post(`/quotations/${id}/accept`),\n    rejectQuotation: (id)=>api.post(`/quotations/${id}/reject`)\n};\nconst vehicleAPI = {\n    getVehicles: (params)=>api.get(\"/vehicles\", {\n            params\n        }),\n    getVehicle: (id)=>api.get(`/vehicles/${id}`),\n    createVehicle: (data)=>api.post(\"/vehicles\", data),\n    updateVehicle: (id, data)=>api.put(`/vehicles/${id}`, data),\n    deleteVehicle: (id)=>api.delete(`/vehicles/${id}`)\n};\nconst legacyTrackingAPI = {\n    getTrackingDetails: (loadId)=>api.get(`/tracking/${loadId}`),\n    updateLocation: (data)=>api.post(\"/tracking/location\", data),\n    getRouteHistory: (loadId)=>api.get(`/tracking/${loadId}/history`)\n};\nconst dashboardAPI = {\n    get: (url)=>api.get(url),\n    getStats: ()=>api.get(\"/dashboard/stats\"),\n    getRecentActivity: ()=>api.get(\"/dashboard/activity\"),\n    getAnalytics: (period)=>api.get(`/dashboard/analytics?period=${period}`)\n};\n// Role-based API endpoints for unified system\nconst adminAPI = {\n    // Dashboard\n    getDashboard: ()=>api.get(\"/admin/dashboard\"),\n    getLoadCount: ()=>api.get(\"/admin/dashboard/load-count\"),\n    getLoadDetails: (data)=>api.post(\"/admin/dashboard/load-details\", data),\n    getLoadAnalysis: (data)=>api.post(\"/admin/dashboard/load-analysis\", data),\n    getBidAnalysis: (data)=>api.post(\"/admin/dashboard/bid-analysis\", data),\n    // Load Management\n    getLoads: (params)=>api.get(\"/admin/loads\", {\n            params\n        }),\n    getCreateLoadData: ()=>api.get(\"/admin/loads/create\"),\n    createLoad: (data)=>api.post(\"/admin/loads/create\", data),\n    getEditLoadData: (id)=>api.get(`/admin/loads/${id}/edit`),\n    updateLoad: (id, data)=>api.post(`/admin/loads/${id}/edit`, data),\n    getAdminLoadDetails: (id)=>api.get(`/admin/loads/${id}/details`),\n    selectQuote: (id, data)=>api.post(`/admin/loads/${id}/select-quote`, data),\n    cancelLoad: (id, data)=>api.post(`/admin/loads/${id}/cancel`, data),\n    startTrip: (id, data)=>api.post(`/admin/loads/${id}/start-trip`, data),\n    // Enterprise Management\n    getEnterprises: (params)=>api.get(\"/admin/enterprises\", {\n            params\n        }),\n    getAddEnterpriseData: ()=>api.get(\"/admin/enterprises/create\"),\n    createEnterprise: (data)=>api.post(\"/admin/enterprises/create\", data),\n    getEnterpriseDetails: (id)=>api.get(`/admin/enterprises/${id}`),\n    verifyEnterprise: (id, data)=>api.post(`/admin/enterprises/${id}/verify`, data),\n    getEditEnterpriseData: (id)=>api.get(`/admin/enterprises/${id}/edit`),\n    updateEnterprise: (id, data)=>api.post(`/admin/enterprises/${id}/update`, data),\n    // User Management\n    getUsers: (params)=>api.get(\"/admin/users\", {\n            params\n        }),\n    getAddUserData: ()=>api.get(\"/admin/users/create\"),\n    createUser: (data)=>api.post(\"/admin/users/create\", data),\n    getEditUserData: (id)=>api.get(`/admin/users/${id}/edit`),\n    updateUser: (id, data)=>api.post(`/admin/users/${id}/update`, data),\n    deactivateUser: (id)=>api.post(`/admin/users/${id}/deactivate`),\n    activateUser: (id)=>api.post(`/admin/users/${id}/activate`),\n    // Vehicle Management\n    getVehicles: (params)=>api.get(\"/admin/vehicles\", {\n            params\n        }),\n    getAddVehicleData: ()=>api.get(\"/admin/vehicles/create\"),\n    createVehicle: (data)=>api.post(\"/admin/vehicles/create\", data),\n    getVehicleDetails: (id)=>api.get(`/admin/vehicles/${id}`),\n    deactivateVehicle: (id)=>api.post(`/admin/vehicles/${id}/deactivate`),\n    approveVehicle: (id)=>api.post(`/admin/vehicles/${id}/approve`),\n    // Reports\n    getReports: ()=>api.get(\"/admin/reports\"),\n    downloadReports: ()=>api.get(\"/admin/reports/download\"),\n    downloadCSV: (data)=>api.post(\"/admin/reports/download-csv\", data),\n    // Helper endpoints\n    getTruckTypes: ()=>api.get(\"/admin/truck-types\"),\n    getMaterialTypes: ()=>api.get(\"/admin/material-types\"),\n    getConsignees: ()=>api.get(\"/admin/consignees\"),\n    getFleetList: ()=>api.get(\"/admin/fleet-list\")\n};\nconst consigneeAPI = {\n    // Dashboard\n    getDashboard: ()=>api.get(\"/consignee/dashboard\"),\n    getLoadCount: ()=>api.get(\"/consignee/dashboard/load-count\"),\n    getLoadDetails: (data)=>api.post(\"/consignee/dashboard/load-details\", data),\n    getLoadAnalysis: (data)=>api.post(\"/consignee/dashboard/load-analysis\", data),\n    getBidAnalysis: (data)=>api.post(\"/consignee/dashboard/bid-analysis\", data),\n    // Load Management\n    getCreateLoadData: (biddingType)=>api.get(`/consignee/loads/create/${biddingType}`),\n    createLoad: (data)=>api.post(\"/consignee/loads/create\", data),\n    getManageLoads: ()=>api.get(\"/consignee/loads/manage\"),\n    getOngoingLoads: (data)=>api.post(\"/consignee/loads/ongoing\", data),\n    getUpcomingLoads: (data)=>api.post(\"/consignee/loads/upcoming\", data),\n    getPastLoads: (data)=>api.post(\"/consignee/loads/past\", data),\n    getArchiveLoads: (data)=>api.post(\"/consignee/loads/archive\", data),\n    getEditLoadData: (id, biddingType)=>api.get(`/consignee/loads/${id}/edit/${biddingType}`),\n    updateLoad: (id, data)=>api.post(`/consignee/loads/${id}/edit`, data),\n    // Quotation Management\n    getQuotationDetails: (id)=>api.get(`/consignee/quotations/${id}/details`),\n    selectQuotation: (data)=>api.post(\"/consignee/quotations/select\", data),\n    selectMultiQuotation: (data)=>api.post(\"/consignee/quotations/select-multi\", data),\n    cancelQuotation: (data)=>api.post(\"/consignee/quotations/cancel\", data),\n    addRateReview: (data)=>api.post(\"/consignee/quotations/rate-review\", data),\n    // Enterprise & Branch Management\n    getEnterpriseProfile: ()=>api.get(\"/consignee/enterprise/profile\"),\n    updateEnterpriseProfile: (data)=>api.post(\"/consignee/enterprise/profile\", data),\n    getBranches: ()=>api.get(\"/consignee/branches\"),\n    getAddBranchData: ()=>api.get(\"/consignee/branches/create\"),\n    createBranch: (data)=>api.post(\"/consignee/branches/create\", data),\n    getEditBranchData: (id)=>api.get(`/consignee/branches/${id}/edit`),\n    updateBranch: (id, data)=>api.post(`/consignee/branches/${id}/update`, data),\n    // User Management\n    getUsers: ()=>api.get(\"/consignee/users\"),\n    createUser: (data)=>api.post(\"/consignee/users/create\", data),\n    getEditUserData: (id)=>api.get(`/consignee/users/${id}/edit`),\n    updateUser: (id, data)=>api.post(`/consignee/users/${id}/update`, data),\n    deleteUser: (id)=>api.get(`/consignee/users/${id}/delete`),\n    activateUser: (id)=>api.get(`/consignee/users/${id}/activate`),\n    updateProfile: (data)=>api.post(\"/consignee/profile/update\", data),\n    // Reports\n    getReports: ()=>api.get(\"/consignee/reports\"),\n    downloadReports: ()=>api.get(\"/consignee/reports/download\"),\n    downloadCSV: (data)=>api.post(\"/consignee/reports/download-csv\", data),\n    getSupplierPerformance: ()=>api.get(\"/consignee/reports/supplier-performance\"),\n    getEpSaving: ()=>api.get(\"/consignee/reports/ep-saving\"),\n    getParticipation: ()=>api.get(\"/consignee/reports/participation\"),\n    // Contracts\n    getContracts: ()=>api.get(\"/consignee/contracts\"),\n    createContract: (data)=>api.post(\"/consignee/contracts/create\", data),\n    getContractBazar: ()=>api.get(\"/consignee/contracts/bazar\"),\n    getPendingContracts: ()=>api.get(\"/consignee/contracts/pending\"),\n    getConfirmedContracts: ()=>api.get(\"/consignee/contracts/confirmed\"),\n    getContractDetails: (id)=>api.get(`/consignee/contracts/${id}/details`)\n};\nconst fleetAPI = {\n    // Dashboard\n    getDashboard: ()=>api.get(\"/fleet/dashboard\"),\n    getLoadHistory: (data)=>api.post(\"/fleet/dashboard/load-history\", data),\n    // Load Acquisition\n    getAcquireLoads: (biddingType)=>api.get(`/fleet/loads/acquire/${biddingType}`),\n    getAcquireLoadsAjax: (biddingType)=>api.get(`/fleet/loads/acquire/ajax/${biddingType}`),\n    filterAcquireLoads: (data)=>api.post(\"/fleet/loads/filter\", data),\n    getLoadDetails: (id)=>api.get(`/fleet/loads/${id}/details`),\n    addQuotes: (data)=>api.post(\"/fleet/loads/quotes/add\", data),\n    submitQuotes: (data)=>api.post(\"/fleet/loads/quotes/submit\", data),\n    // Load Status Management\n    getActiveLoads: (biddingType)=>api.get(`/fleet/loads/active/${biddingType}`),\n    getPendingLoads: (biddingType)=>api.get(`/fleet/loads/pending/${biddingType}`),\n    getConfirmedLoads: (biddingType)=>api.get(`/fleet/loads/confirmed/${biddingType}`),\n    getClosedLoads: ()=>api.get(\"/fleet/loads/closed\"),\n    // My Loads\n    getMyLoads: ()=>api.get(\"/fleet/my-loads\"),\n    getMyOngoingLoads: (data)=>api.post(\"/fleet/my-loads/ongoing\", data),\n    getMyActiveLoads: (data)=>api.post(\"/fleet/my-loads/active\", data),\n    getMyPastLoads: (data)=>api.post(\"/fleet/my-loads/past\", data),\n    getMyArchiveLoads: (data)=>api.post(\"/fleet/my-loads/archive\", data),\n    getTripDetails: (id)=>api.get(`/fleet/loads/${id}/trip-details`),\n    updateQuotes: (data)=>api.post(\"/fleet/loads/quotes/update\", data),\n    startTrip: (data)=>api.post(\"/fleet/loads/start-trip\", data),\n    // Vehicle Management\n    getVehicles: ()=>api.get(\"/fleet/vehicles\"),\n    getAddVehicleData: ()=>api.get(\"/fleet/vehicles/create\"),\n    createVehicle: (data)=>api.post(\"/fleet/vehicles/create\", data),\n    getVehicleDetails: (id)=>api.get(`/fleet/vehicles/${id}/details`),\n    deactivateVehicle: (id)=>api.post(`/fleet/vehicles/${id}/deactivate`),\n    addVehicleDocument: (data)=>api.post(\"/fleet/vehicles/documents/add\", data),\n    deactivateVehicleDocument: (data)=>api.post(\"/fleet/vehicles/documents/deactivate\", data),\n    // Enterprise Management\n    getEnterpriseProfile: ()=>api.get(\"/fleet/enterprise/profile\"),\n    updateEnterpriseProfile: (data)=>api.post(\"/fleet/enterprise/profile\", data),\n    addEnterpriseDocument: (data)=>api.post(\"/fleet/enterprise/documents/add\", data),\n    addBank: (data)=>api.post(\"/fleet/enterprise/banks/add\", data),\n    updateBank: (data)=>api.post(\"/fleet/enterprise/banks/update\", data),\n    deactivateBank: (data)=>api.post(\"/fleet/enterprise/banks/deactivate\", data),\n    // User Management\n    getUsers: ()=>api.get(\"/fleet/users\"),\n    createUser: (data)=>api.post(\"/fleet/users/create\", data),\n    getEditUserData: (id)=>api.get(`/fleet/users/${id}/edit`),\n    updateUser: (id, data)=>api.post(`/fleet/users/${id}/update`, data),\n    deleteUser: (id)=>api.get(`/fleet/users/${id}/delete`),\n    activateUser: (id)=>api.get(`/fleet/users/${id}/activate`),\n    updateProfile: (data)=>api.post(\"/fleet/profile/update\", data),\n    // Contracts\n    getContracts: ()=>api.get(\"/fleet/contracts\"),\n    getContractDetails: (id)=>api.get(`/fleet/contracts/${id}/details`),\n    submitQuotation: (data)=>api.post(\"/fleet/contracts/quotation\", data),\n    getConfirmedContracts: ()=>api.get(\"/fleet/contracts/confirmed\"),\n    getPendingContracts: ()=>api.get(\"/fleet/contracts/pending\"),\n    // Reports\n    getReports: ()=>api.get(\"/fleet/reports\"),\n    getPerformanceReports: ()=>api.get(\"/fleet/reports/performance\"),\n    getVehicleReports: (data)=>api.post(\"/fleet/reports/vehicle\", data),\n    getRevenueReports: (data)=>api.post(\"/fleet/reports/revenue\", data),\n    getPerformanceImprovement: (data)=>api.post(\"/fleet/reports/performance-improvement\", data),\n    getFreightVariance: (data)=>api.post(\"/fleet/reports/freight-variance\", data),\n    getParticipationReports: (data)=>api.post(\"/fleet/reports/participation\", data),\n    getPodReports: (data)=>api.post(\"/fleet/reports/pod\", data)\n};\n// Tracking system API endpoints\nconst trackingAPI = {\n    // Authentication\n    login: (mobile)=>api.post(\"/tracking/login\", {\n            mobile\n        }),\n    verifyOtp: (mobile, otp)=>api.post(\"/tracking/login/verify-otp\", {\n            mobile,\n            otp\n        }),\n    // Dashboard\n    getTrackingCount: (data)=>api.post(\"/tracking/dashboard/tracking-count\", data),\n    getTrackingHistory: (data)=>api.post(\"/tracking/dashboard/tracking-history\", data),\n    getTrackingAnalysis: (data)=>api.post(\"/tracking/dashboard/tracking-analysis\", data),\n    // Tracking Operations\n    searchTracking: (data)=>api.post(\"/tracking/tracking/search\", data),\n    getTrackingList: (data)=>api.post(\"/tracking/tracking/list\", data),\n    changeTrackingStatus: (data)=>api.post(\"/tracking/tracking/status/change\", data),\n    getTrackingDetails: (data)=>api.post(\"/tracking/tracking/details\", data),\n    createTracking: (data)=>api.post(\"/tracking/tracking/create\", data),\n    editTracking: (data)=>api.post(\"/tracking/tracking/edit\", data),\n    // Organization Management\n    getOrganizationList: (data)=>api.post(\"/tracking/organization/list\", data),\n    searchOrganization: (data)=>api.post(\"/tracking/organization/search\", data),\n    getOrganizationById: (orgId, data)=>api.post(`/tracking/organization/${orgId}`, data),\n    editOrganization: (orgId, data)=>api.post(`/tracking/organization/${orgId}/edit`, data),\n    createOrganization: (data)=>api.post(\"/tracking/organization/create\", data),\n    // Reports\n    generateReport: (data)=>api.post(\"/tracking/reports/generate\", data),\n    generateReportHtml: (data)=>api.post(\"/tracking/reports/data\", data),\n    generateMisReport: (data)=>api.post(\"/tracking/reports/mis/generate\", data),\n    // Delivery Consent\n    sendOtp: (data)=>api.post(\"/tracking/delivery-consent/send-otp\", data),\n    getDeliveryTrackingDetails: (data)=>api.post(\"/tracking/delivery-consent/tracking-details\", data),\n    getConsentStatus: (data)=>api.post(\"/tracking/delivery-consent/status\", data)\n};\n// Master data API endpoints (public)\nconst masterDataAPI = {\n    getTruckTypes: ()=>api.get(\"/master-data/truck-types\"),\n    getMaterialTypes: ()=>api.get(\"/master-data/material-types\"),\n    getCountries: ()=>api.get(\"/master-data/countries\"),\n    getStates: (countryId)=>api.get(`/master-data/states${countryId ? `?countryId=${countryId}` : \"\"}`),\n    getCities: (stateId)=>api.get(`/master-data/cities${stateId ? `?stateId=${stateId}` : \"\"}`)\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/authStore.ts":
/*!********************************!*\
  !*** ./src/store/authStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set, get)=>({\n        // Initial state\n        user: null,\n        accessToken: null,\n        refreshToken: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        // Actions\n        login: async (mobile, password)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.api.post(\"/auth/login\", {\n                    mobile,\n                    password\n                });\n                const { user, accessToken, refreshToken } = response.data.data;\n                // Store tokens in cookies\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"accessToken\", accessToken, {\n                    expires: 1\n                }); // 1 day\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refreshToken\", refreshToken, {\n                    expires: 7\n                }); // 7 days\n                set({\n                    user,\n                    accessToken,\n                    refreshToken,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || \"Login failed\";\n                set({\n                    error: errorMessage,\n                    isLoading: false,\n                    isAuthenticated: false\n                });\n                throw new Error(errorMessage);\n            }\n        },\n        register: async (userData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.api.post(\"/auth/register\", userData);\n                const { user, accessToken, refreshToken } = response.data.data;\n                // Store tokens in cookies\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"accessToken\", accessToken, {\n                    expires: 1\n                });\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refreshToken\", refreshToken, {\n                    expires: 7\n                });\n                set({\n                    user,\n                    accessToken,\n                    refreshToken,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || \"Registration failed\";\n                set({\n                    error: errorMessage,\n                    isLoading: false,\n                    isAuthenticated: false\n                });\n                throw new Error(errorMessage);\n            }\n        },\n        smsOtpLogin: (user, accessToken, refreshToken)=>{\n            // Store tokens in cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"accessToken\", accessToken, {\n                expires: 1\n            }); // 1 day\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refreshToken\", refreshToken, {\n                expires: 7\n            }); // 7 days\n            // Transform user data to match frontend interface\n            const transformedUser = {\n                id: user.id,\n                name: user.name,\n                email: user.email,\n                mobile: user.mobile.toString(),\n                profilePic: user.profilePic,\n                userType: user.userType?.toUpperCase() || \"ADMIN\",\n                enterpriseId: user.enterprise?.id || 0,\n                enterprise: user.enterprise ? {\n                    id: user.enterprise.id,\n                    organisationName: user.enterprise.organisationName || \"\",\n                    type: user.enterprise.type || \"\",\n                    isVerified: true\n                } : {\n                    id: 0,\n                    organisationName: \"\",\n                    type: \"\",\n                    isVerified: false\n                },\n                isAdmin: user.isAdmin === \"yes\" || user.isAdmin === true,\n                isManager: user.isManager === \"yes\" || user.isManager === true,\n                isDriver: user.isDriver === \"yes\" || user.isDriver === true,\n                status: user.status?.toUpperCase() || \"ACTIVE\"\n            };\n            set({\n                user: transformedUser,\n                accessToken,\n                refreshToken,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null\n            });\n        },\n        setUser: (user)=>{\n            set({\n                user\n            });\n        },\n        setTokens: (accessToken, refreshToken)=>{\n            // Store tokens in cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"accessToken\", accessToken, {\n                expires: 1\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refreshToken\", refreshToken, {\n                expires: 7\n            });\n            set({\n                accessToken,\n                refreshToken,\n                isAuthenticated: true\n            });\n        },\n        logout: ()=>{\n            // Clear tokens from cookies\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"accessToken\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refreshToken\");\n            // Call logout API\n            _lib_api__WEBPACK_IMPORTED_MODULE_1__.api.post(\"/auth/logout\").catch(()=>{\n            // Ignore errors on logout\n            });\n            set({\n                user: null,\n                accessToken: null,\n                refreshToken: null,\n                isAuthenticated: false,\n                error: null\n            });\n        },\n        refreshAccessToken: async ()=>{\n            const { refreshToken } = get();\n            if (!refreshToken) {\n                get().logout();\n                return;\n            }\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.api.post(\"/auth/refresh\", {\n                    refreshToken\n                });\n                const { accessToken, refreshToken: newRefreshToken } = response.data.data;\n                // Update tokens in cookies\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"accessToken\", accessToken, {\n                    expires: 1\n                });\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refreshToken\", newRefreshToken, {\n                    expires: 7\n                });\n                set({\n                    accessToken,\n                    refreshToken: newRefreshToken\n                });\n            } catch (error) {\n                get().logout();\n                throw error;\n            }\n        },\n        updateUser: (userData)=>{\n            const { user } = get();\n            if (user) {\n                set({\n                    user: {\n                        ...user,\n                        ...userData\n                    }\n                });\n            }\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        initializeAuth: ()=>{\n            const accessToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"accessToken\");\n            const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refreshToken\");\n            if (accessToken && refreshToken) {\n                set({\n                    accessToken,\n                    refreshToken,\n                    isAuthenticated: true,\n                    isLoading: true\n                });\n                // Try to fetch current user data, but don't fail if it doesn't work\n                _lib_api__WEBPACK_IMPORTED_MODULE_1__.api.get(\"/auth/me\").then((response)=>{\n                    const userData = response.data.data.user;\n                    // Transform user data to match frontend interface\n                    const transformedUser = {\n                        id: userData.id,\n                        name: userData.name,\n                        email: userData.email,\n                        mobile: userData.mobile.toString(),\n                        profilePic: userData.profilePic,\n                        userType: userData.userType?.toUpperCase() || \"ADMIN\",\n                        enterpriseId: userData.enterprise?.id || 0,\n                        enterprise: userData.enterprise ? {\n                            id: userData.enterprise.id,\n                            organisationName: userData.enterprise.organisationName || \"\",\n                            type: userData.enterprise.type || \"\",\n                            isVerified: true\n                        } : {\n                            id: 0,\n                            organisationName: \"\",\n                            type: \"\",\n                            isVerified: false\n                        },\n                        isAdmin: userData.isAdmin === \"yes\" || userData.isAdmin === true,\n                        isManager: userData.isManager === \"yes\" || userData.isManager === true,\n                        isDriver: userData.isDriver === \"yes\" || userData.isDriver === true,\n                        status: userData.status?.toUpperCase() || \"ACTIVE\"\n                    };\n                    set({\n                        user: transformedUser,\n                        isLoading: false\n                    });\n                }).catch((error)=>{\n                    console.log(\"Failed to fetch user data, but keeping tokens for now\", error);\n                    // Don't logout immediately, just set loading to false\n                    // Keep the user authenticated with existing tokens\n                    set({\n                        isLoading: false\n                    });\n                });\n            } else {\n                // No tokens found, ensure we're logged out\n                set({\n                    user: null,\n                    accessToken: null,\n                    refreshToken: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            accessToken: state.accessToken,\n            refreshToken: state.refreshToken,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/authStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/socketStore.ts":
/*!**********************************!*\
  !*** ./src/store/socketStore.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocketStore: () => (/* binding */ useSocketStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n\n\nconst useSocketStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        // Initial state\n        socket: null,\n        isConnected: false,\n        error: null,\n        // Actions\n        connect: (token)=>{\n            const { socket } = get();\n            if (socket?.connected) {\n                return;\n            }\n            const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(\"http://localhost:3001\" || 0, {\n                auth: {\n                    token\n                },\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ]\n            });\n            newSocket.on(\"connect\", ()=>{\n                set({\n                    isConnected: true,\n                    error: null\n                });\n                console.log(\"Socket connected\");\n            });\n            newSocket.on(\"disconnect\", ()=>{\n                set({\n                    isConnected: false\n                });\n                console.log(\"Socket disconnected\");\n            });\n            newSocket.on(\"connect_error\", (error)=>{\n                set({\n                    error: error.message,\n                    isConnected: false\n                });\n                console.error(\"Socket connection error:\", error);\n            });\n            set({\n                socket: newSocket\n            });\n        },\n        disconnect: ()=>{\n            const { socket } = get();\n            if (socket) {\n                socket.disconnect();\n                set({\n                    socket: null,\n                    isConnected: false,\n                    error: null\n                });\n            }\n        },\n        joinRoom: (room)=>{\n            const { socket } = get();\n            if (socket?.connected) {\n                socket.emit(\"join-room\", room);\n            }\n        },\n        leaveRoom: (room)=>{\n            const { socket } = get();\n            if (socket?.connected) {\n                socket.emit(\"leave-room\", room);\n            }\n        },\n        emit: (event, data)=>{\n            const { socket } = get();\n            if (socket?.connected) {\n                socket.emit(event, data);\n            }\n        },\n        on: (event, callback)=>{\n            const { socket } = get();\n            if (socket) {\n                socket.on(event, callback);\n            }\n        },\n        off: (event)=>{\n            const { socket } = get();\n            if (socket) {\n                socket.off(event);\n            }\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/socketStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"888923c63d44\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdW5pZmllZC10cmFuc3BvcnQtZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2IxODIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ODg5MjNjNjNkNDRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/dashboard/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction AdminDashboard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"Admin Dashboard\"\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 1,\n        columnNumber: 52\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FkbWluL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0E7SUFBbUIscUJBQVEsOERBQUNDO2tCQUFJOzs7Ozs7QUFBd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly91bmlmaWVkLXRyYW5zcG9ydC1mcm9udGVuZC8uL3NyYy9hcHAvYWRtaW4vZGFzaGJvYXJkL3BhZ2UudHN4PzRlNmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWRtaW5EYXNoYm9hcmQoKSB7IHJldHVybiAoPGRpdj5BZG1pbiBEYXNoYm9hcmQ8L2Rpdj4pOyB9XHJcbiJdLCJuYW1lcyI6WyJBZG1pbkRhc2hib2FyZCIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/admin/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(rsc)/./src/components/layout/DashboardLayout.tsx\");\n\n\n\n\n\n\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    themeColor: \"#0e2d67\"\n};\nconst metadata = {\n    title: \"Unified Transport Platform\",\n    description: \"Real-time load posting, bidding, and vehicle tracking platform\",\n    keywords: [\n        \"transport\",\n        \"logistics\",\n        \"bidding\",\n        \"tracking\",\n        \"real-time\"\n    ],\n    authors: [\n        {\n            name: \"Transport Platform Team\"\n        }\n    ],\n    // manifest: '/manifest.json', // Temporarily disabled\n    icons: {\n        icon: \"/favicon.ico\"\n    },\n    openGraph: {\n        title: \"Unified Transport Platform\",\n        description: \"Real-time load posting, bidding, and vehicle tracking platform\",\n        type: \"website\",\n        locale: \"en_US\",\n        siteName: \"Unified Transport Platform\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Unified Transport Platform\",\n        description: \"Real-time load posting, bidding, and vehicle tracking platform\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#fff\",\n                                color: \"#374151\",\n                                boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\n                                border: \"1px solid #e5e7eb\",\n                                borderRadius: \"0.5rem\",\n                                padding: \"12px 16px\",\n                                fontSize: \"14px\",\n                                maxWidth: \"400px\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                },\n                                style: {\n                                    borderLeft: \"4px solid #22c55e\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                },\n                                style: {\n                                    borderLeft: \"4px solid #ef4444\"\n                                }\n                            },\n                            loading: {\n                                iconTheme: {\n                                    primary: \"#6366f1\",\n                                    secondary: \"#fff\"\n                                },\n                                style: {\n                                    borderLeft: \"4px solid #6366f1\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\eparivahan\augment_june_26_26\new_system\frontend\src\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\eparivahan\augment_june_26_26\new_system\frontend\src\components\layout\DashboardLayout.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/engine.io-client","vendor-chunks/@heroicons","vendor-chunks/socket.io-client","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/socket.io-parser","vendor-chunks/es-errors","vendor-chunks/@swc","vendor-chunks/call-bind-apply-helpers","vendor-chunks/engine.io-parser","vendor-chunks/use-sync-external-store","vendor-chunks/debug","vendor-chunks/zustand","vendor-chunks/get-proto","vendor-chunks/react-hot-toast","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/js-cookie","vendor-chunks/goober","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ceparivahan%5Caugment_june_26_26%5Cnew_system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();