"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/components/noop-head.js ***!
  \***************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return NoopHead;\n    }\n}));\nfunction NoopHead() {\n    return null;\n}\n_c = NoopHead;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=noop-head.js.map\nvar _c;\n$RefreshReg$(_c, \"NoopHead\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm9vcC1oZWFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBQUE7OztlQUF3QkE7OztBQUFULFNBQVNBO0lBQ3RCLE9BQU87QUFDVDtLQUZ3QkEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9ub29wLWhlYWQudHN4PzlkNTYiXSwibmFtZXMiOlsiTm9vcEhlYWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { smsOtpLogin } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // 1: mobile, 2: otp, 3: success\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        login_mobile: \"\",\n        user_otp: \"\",\n        user_id: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Step 1: Send OTP\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/auth/bypass/get-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    login_mobile: formData.login_mobile\n                })\n            });\n            const data = await response.json();\n            if (data.alert === \"success\") {\n                setFormData((prev)=>({\n                        ...prev,\n                        user_id: data.user_id\n                    }));\n                setStep(2);\n            } else {\n                setError(data.message || \"Failed to send OTP\");\n            }\n        } catch (err) {\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Step 2: Verify OTP\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"Sending OTP verification request:\", {\n                user_id: formData.user_id,\n                user_otp: formData.user_otp\n            });\n            const response = await fetch(\"/api/auth/bypass/login-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    user_id: formData.user_id,\n                    user_otp: formData.user_otp\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            const data = await response.json();\n            console.log(\"Response data:\", data);\n            if (data.alert === \"success\") {\n                console.log(\"Authentication successful, response data:\", data);\n                // Show success step first\n                setStep(3);\n                // Store authentication data in localStorage as backup\n                localStorage.setItem(\"token\", data.token);\n                localStorage.setItem(\"user\", JSON.stringify(data.user));\n                localStorage.setItem(\"isAuthenticated\", \"true\");\n                console.log(\"Stored authentication data in localStorage\");\n                // Immediate redirect using window.location (most reliable)\n                const redirectUrl = \"/dashboard\";\n                console.log(\"Redirecting immediately to:\", redirectUrl);\n                // Use window.location for immediate redirect\n                window.location.href = redirectUrl;\n            } else {\n                console.log(\"Authentication failed:\", data);\n                setError(data.message || \"Invalid OTP\");\n            }\n        } catch (err) {\n            console.error(\"Network error during OTP verification:\", err);\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#0e2d67] via-[#1e3a8a] to-[#e3000f] flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-16 h-16 bg-gradient-to-r from-[#0e2d67] to-[#e3000f] rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"eParivahan Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: step === 1 ? \"Enter your mobile number\" : step === 2 ? \"Enter the OTP sent to your mobile\" : \"Login successful! Redirecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this),\n                step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"refresh\",\n                                content: \"2;url=/dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    children: \"✅ Login successful! Redirecting to dashboard...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3 text-sm\",\n                                    children: \"If not redirected automatically, click below:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dashboard\",\n                                    className: \"inline-block bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                    children: \"Go to Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Mobile Number \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 31\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: formData.login_mobile,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        login_mobile: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                            placeholder: \"Enter 10-digit mobile number\",\n                                            pattern: \"[6-9][0-9]{9}\",\n                                            maxLength: \"10\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: \"Enter mobile number starting with 6, 7, 8, or 9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading || formData.login_mobile.length !== 10,\n                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Sending OTP...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 17\n                            }, this) : \"Send OTP\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleVerifyOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Enter OTP \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.user_otp,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        user_otp: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent text-center text-lg tracking-widest\",\n                                            placeholder: \"000000\",\n                                            maxLength: \"6\",\n                                            pattern: \"[0-9]{6}\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: [\n                                        \"OTP sent to +91 \",\n                                        formData.login_mobile\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setStep(1),\n                                    className: \"flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg hover:bg-gray-200 transition-colors\",\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || formData.user_otp.length !== 6,\n                                    className: \"flex-1 bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Verifying...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Login\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 eParivahan. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"LVXK5e1QL+GynZVgxj92VNUDRVI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ })

});