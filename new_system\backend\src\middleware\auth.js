const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

const authMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'No token provided, authorization denied'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from REAL database (ep_new_db_july with public schema) - simplified query first
    const users = await prisma.$queryRaw`
      SELECT u.*, e.organisation_name
      FROM public.users u
      LEFT JOIN public.enterprises e ON u.enterprise_id = e.id
      WHERE u.id = ${decoded.userId}::bigint
      LIMIT 1
    `;

    const user = users.length > 0 ? users[0] : null;

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Token is not valid'
      });
    }

    if (user.status !== 'ACTIVE' && user.status !== 'active') {
      return res.status(401).json({
        success: false,
        error: 'User account is not active'
      });
    }

    // Add user to request object with legacy database structure
    req.user = {
      id: user.id,
      name: user.name,
      email: user.email,
      mobile: user.mobile,
      userType: user.user_type,
      enterpriseId: user.enterprise_id,
      enterprise: user.enterprise_type ? {
        id: user.enterprise_id,
        organisationName: user.organisation_name,
        type: user.enterprise_type
      } : null,
      isAdmin: user.is_admin,
      isManager: user.is_manager,
      isDriver: user.is_driver
    };

    next();
  } catch (error) {
    logger.error('Auth middleware error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Server error in authentication'
    });
  }
};

// Admin only middleware
const adminOnly = (req, res, next) => {
  if (!req.user.isAdmin) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. Admin privileges required.'
    });
  }
  next();
};

// Manager or Admin middleware
const managerOrAdmin = (req, res, next) => {
  if (!req.user.isAdmin && !req.user.isManager) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. Manager or Admin privileges required.'
    });
  }
  next();
};

// Same enterprise middleware
const sameEnterprise = (req, res, next) => {
  const targetEnterpriseId = parseInt(req.params.enterpriseId) || parseInt(req.body.enterpriseId);
  
  if (req.user.enterpriseId !== targetEnterpriseId && !req.user.isAdmin) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only access your own enterprise data.'
    });
  }
  next();
};

// Optional auth middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        enterprise: true
      }
    });

    if (user && user.status === 'ACTIVE') {
      req.user = {
        id: user.id,
        name: user.name,
        email: user.email,
        mobile: user.mobile,
        userType: user.userType,
        enterpriseId: user.enterpriseId,
        enterprise: user.enterprise,
        isAdmin: user.isAdmin,
        isManager: user.isManager,
        isDriver: user.isDriver
      };
    }

    next();
  } catch (error) {
    // Don't fail, just continue without user
    next();
  }
};

/**
 * Tracking System Access Token Authentication (Legacy Support)
 * This middleware supports the access_token authentication used in tracking_system
 */
const trackingAuthMiddleware = async (req, res, next) => {
  try {
    // Get access token from headers (tracking system style)
    const { access_token } = req.headers;

    if (!access_token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      });
    }

    // For now, we'll validate the access token by checking if it matches a user's mobile
    // In a real implementation, you'd have a proper token validation system
    const users = await prisma.$queryRaw`
      SELECT u.*, e."organisationName" as organisation_name, e.type as enterprise_type
      FROM public.users u
      LEFT JOIN public.enterprises e ON u."enterpriseId" = e.id
      WHERE u.mobile IS NOT NULL AND u.status = 'ACTIVE'
      LIMIT 1
    `;

    const user = users.length > 0 ? users[0] : null;

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid access token'
      });
    }

    // Add user to request object with tracking system compatible structure
    req.user = {
      id: user.id,
      name: user.name,
      email: user.email,
      mobile: user.mobile,
      userType: user.user_type,
      enterpriseId: user.enterprise_id,
      enterprise: user.enterprise_type ? {
        id: user.enterprise_id,
        organisationName: user.organisation_name,
        type: user.enterprise_type
      } : null,
      isAdmin: user.is_admin,
      isManager: user.is_manager,
      isDriver: user.is_driver
    };

    next();
  } catch (error) {
    logger.error('Tracking auth middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Flexible authentication middleware that supports both JWT and access tokens
 */
const flexibleAuthMiddleware = async (req, res, next) => {
  // Check if it's a tracking system request (has access_token header)
  if (req.headers.access_token) {
    return trackingAuthMiddleware(req, res, next);
  }

  // Otherwise use standard JWT authentication
  return authMiddleware(req, res, next);
};

module.exports = {
  authMiddleware,
  trackingAuthMiddleware,
  flexibleAuthMiddleware,
  adminOnly,
  managerOrAdmin,
  sameEnterprise,
  optionalAuth
};
