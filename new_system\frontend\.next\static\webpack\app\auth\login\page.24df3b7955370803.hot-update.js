"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// SMS OTP Login Schema (Mobile-only login as per legacy system)\nconst mobileLoginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    mobile: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().regex(/^[6-9]\\d{9}$/, \"Mobile number must be 10 digits starting with 6-9\").length(10, \"Mobile number must be exactly 10 digits\")\n});\n// OTP Verification Schema\nconst otpVerificationSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    otp: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().length(6, \"OTP must be exactly 6 digits\").regex(/^\\d{6}$/, \"OTP must contain only numbers\")\n});\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoading } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    const [mobile, setMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [otpSent, setOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Mobile login form\n    const mobileForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(mobileLoginSchema)\n    });\n    // OTP verification form\n    const otpForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(otpVerificationSchema)\n    });\n    // Send OTP\n    const onMobileSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/get-otp\", {\n                login_mobile: data.mobile\n            });\n            if (response.data.success) {\n                setMobile(data.mobile);\n                setUserId(response.data.data.userId);\n                setStep(\"otp\");\n                setOtpSent(true);\n                setCountdown(120); // 2 minutes countdown\n                // Start countdown timer\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP sent to your mobile number!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.error || \"Failed to send OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to send OTP\");\n        }\n    };\n    // Verify OTP\n    const onOtpSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/verify-otp\", {\n                mobile,\n                otp: data.otp\n            });\n            if (response.data.success) {\n                const { tokens, user } = response.data.data;\n                // Update auth store state (this will also handle localStorage/cookies)\n                const authStore = _store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore.getState();\n                authStore.smsOtpLogin(user, tokens.accessToken, tokens.refreshToken);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Login successful!\");\n                // Redirect to main dashboard (all user types use the same dashboard)\n                router.push(\"/dashboard\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.error || \"Invalid OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"OTP verification failed\");\n        }\n    };\n    // Resend OTP\n    const resendOtp = async ()=>{\n        if (countdown > 0) return;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/mobile-login\", {\n                mobile\n            });\n            if (response.data.success) {\n                setCountdown(120);\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP resent successfully!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to resend OTP\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                        backgroundSize: \"60px 60px\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 w-16 rounded-2xl bg-gradient-to-br from-[#0e2d67] to-[#1a4480] flex items-center justify-center shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-[#e3000f] flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-xs\",\n                                        children: \"EP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: step === \"mobile\" ? \"Welcome to eParivahan\" : \"Verify OTP\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: step === \"mobile\" ? \"Enter your mobile number to receive OTP\" : \"OTP sent to \".concat(mobile, \". Enter the 6-digit code.\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8\",\n                    children: [\n                        step === \"mobile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: mobileForm.handleSubmit(onMobileSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"mobile\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Mobile Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...mobileForm.register(\"mobile\"),\n                                                    type: \"tel\",\n                                                    autoComplete: \"tel\",\n                                                    maxLength: 10,\n                                                    className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(mobileForm.formState.errors.mobile ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-lg tracking-wider\"),\n                                                    placeholder: \"9876543210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"+91\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        mobileForm.formState.errors.mobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this),\n                                                mobileForm.formState.errors.mobile.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Sending OTP...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Send OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this),\n                        step === \"otp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: otpForm.handleSubmit(onOtpSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"otp\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Enter OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...otpForm.register(\"otp\"),\n                                                type: \"text\",\n                                                maxLength: 6,\n                                                className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(otpForm.formState.errors.otp ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-center text-2xl tracking-widest font-mono\"),\n                                                placeholder: \"000000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        otpForm.formState.errors.otp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this),\n                                                otpForm.formState.errors.otp.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this),\n                                countdown > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Resend OTP in \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-[#0e2d67]\",\n                                                children: [\n                                                    Math.floor(countdown / 60),\n                                                    \":\",\n                                                    (countdown % 60).toString().padStart(2, \"0\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verifying...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verify & Login\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: resendOtp,\n                                            disabled: countdown > 0,\n                                            className: \"w-full border-2 border-gray-200 text-gray-700 font-semibold py-2 px-4 rounded-xl hover:border-[#0e2d67] hover:text-[#0e2d67] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 bg-white/50 backdrop-blur-sm\",\n                                            children: countdown > 0 ? \"Please wait...\" : \"Resend OTP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                setStep(\"mobile\");\n                                                setCountdown(0);\n                                                otpForm.reset();\n                                            },\n                                            className: \"w-full text-gray-600 font-medium py-2 px-4 rounded-xl hover:text-[#0e2d67] focus:outline-none transition-all duration-200\",\n                                            children: \"← Change Mobile Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-100 rounded-2xl p-6 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg bg-blue-500 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"\\uD83D\\uDD11\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-bold text-blue-900\",\n                                    children: \"Demo Login Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Admin User\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8240301895\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Consignee (Load Poster)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9874896900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8181816477\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9051720133\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs font-semibold text-blue-800 mb-2\",\n                                    children: \"Default OTPs (Admin Access)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-2\",\n                                    children: [\n                                        \"123456\",\n                                        \"111111\",\n                                        \"000000\",\n                                        \"999999\",\n                                        \"555555\"\n                                    ].map((otp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-2 text-center border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs font-mono text-blue-900\",\n                                                children: otp\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, otp, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-2\",\n                                    children: \"These OTPs work for any mobile number for testing purposes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"ggp3cQO+eSU+p6y29aLI5bJzFZc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXV0aC9sb2dpbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQ1c7QUFDRjtBQUNZO0FBQzlCO0FBQ1k7QUFDYTtBQUMrQztBQUNoRTtBQUVoQyxnRUFBZ0U7QUFDaEUsTUFBTVcsb0JBQW9CUCx5Q0FBUSxDQUFDO0lBQ2pDUyxRQUFRVCx5Q0FBUSxHQUNiVyxLQUFLLENBQUMsZ0JBQWdCLHFEQUN0QkMsTUFBTSxDQUFDLElBQUk7QUFDaEI7QUFFQSwwQkFBMEI7QUFDMUIsTUFBTUMsd0JBQXdCYix5Q0FBUSxDQUFDO0lBQ3JDYyxLQUFLZCx5Q0FBUSxHQUNWWSxNQUFNLENBQUMsR0FBRyxnQ0FDVkQsS0FBSyxDQUFDLFdBQVc7QUFDdEI7QUFLZSxTQUFTSTs7SUFDdEIsTUFBTUMsU0FBU25CLDBEQUFTQTtJQUN4QixNQUFNLEVBQUVvQixTQUFTLEVBQUUsR0FBR2YsOERBQVlBO0lBQ2xDLE1BQU0sQ0FBQ2dCLE1BQU1DLFFBQVEsR0FBR3ZCLCtDQUFRQSxDQUFtQjtJQUNuRCxNQUFNLENBQUNhLFFBQVFXLFVBQVUsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ3lCLFFBQVFDLFVBQVUsR0FBRzFCLCtDQUFRQSxDQUFnQjtJQUNwRCxNQUFNLENBQUMyQixTQUFTQyxXQUFXLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM2QixXQUFXQyxhQUFhLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUUzQyxvQkFBb0I7SUFDcEIsTUFBTStCLGFBQWE3Qix3REFBT0EsQ0FBa0I7UUFDMUM4QixVQUFVN0Isb0VBQVdBLENBQUNRO0lBQ3hCO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1zQixVQUFVL0Isd0RBQU9BLENBQXNCO1FBQzNDOEIsVUFBVTdCLG9FQUFXQSxDQUFDYztJQUN4QjtJQUVBLFdBQVc7SUFDWCxNQUFNaUIsaUJBQWlCLE9BQU9DO1FBQzVCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU0xQix5Q0FBR0EsQ0FBQzJCLElBQUksQ0FBQyx3QkFBd0I7Z0JBQUVDLGNBQWNILEtBQUt0QixNQUFNO1lBQUM7WUFFcEYsSUFBSXVCLFNBQVNELElBQUksQ0FBQ0ksT0FBTyxFQUFFO2dCQUN6QmYsVUFBVVcsS0FBS3RCLE1BQU07Z0JBQ3JCYSxVQUFVVSxTQUFTRCxJQUFJLENBQUNBLElBQUksQ0FBQ1YsTUFBTTtnQkFDbkNGLFFBQVE7Z0JBQ1JLLFdBQVc7Z0JBQ1hFLGFBQWEsTUFBTSxzQkFBc0I7Z0JBRXpDLHdCQUF3QjtnQkFDeEIsTUFBTVUsUUFBUUMsWUFBWTtvQkFDeEJYLGFBQWEsQ0FBQ1k7d0JBQ1osSUFBSUEsUUFBUSxHQUFHOzRCQUNiQyxjQUFjSDs0QkFDZCxPQUFPO3dCQUNUO3dCQUNBLE9BQU9FLE9BQU87b0JBQ2hCO2dCQUNGLEdBQUc7Z0JBRUhyQyx1REFBS0EsQ0FBQ2tDLE9BQU8sQ0FBQztZQUNoQixPQUFPO2dCQUNMbEMsdURBQUtBLENBQUN1QyxLQUFLLENBQUNSLFNBQVNELElBQUksQ0FBQ1MsS0FBSyxJQUFJO1lBQ3JDO1FBQ0YsRUFBRSxPQUFPQSxPQUFZO2dCQUNQQSxzQkFBQUE7WUFBWnZDLHVEQUFLQSxDQUFDdUMsS0FBSyxDQUFDQSxFQUFBQSxrQkFBQUEsTUFBTVIsUUFBUSxjQUFkUSx1Q0FBQUEsdUJBQUFBLGdCQUFnQlQsSUFBSSxjQUFwQlMsMkNBQUFBLHFCQUFzQkEsS0FBSyxLQUFJO1FBQzdDO0lBQ0Y7SUFFQSxhQUFhO0lBQ2IsTUFBTUMsY0FBYyxPQUFPVjtRQUN6QixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNMUIseUNBQUdBLENBQUMyQixJQUFJLENBQUMsb0JBQW9CO2dCQUNsRHhCO2dCQUNBSyxLQUFLaUIsS0FBS2pCLEdBQUc7WUFDZjtZQUVBLElBQUlrQixTQUFTRCxJQUFJLENBQUNJLE9BQU8sRUFBRTtnQkFDekIsTUFBTSxFQUFFTyxNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHWCxTQUFTRCxJQUFJLENBQUNBLElBQUk7Z0JBRTNDLHVFQUF1RTtnQkFDdkUsTUFBTWEsWUFBWTFDLDBEQUFZQSxDQUFDMkMsUUFBUTtnQkFDdkNELFVBQVVFLFdBQVcsQ0FBQ0gsTUFBTUQsT0FBT0ssV0FBVyxFQUFFTCxPQUFPTSxZQUFZO2dCQUVuRS9DLHVEQUFLQSxDQUFDa0MsT0FBTyxDQUFDO2dCQUVkLHFFQUFxRTtnQkFDckVuQixPQUFPaUMsSUFBSSxDQUFDO1lBQ2QsT0FBTztnQkFDTGhELHVEQUFLQSxDQUFDdUMsS0FBSyxDQUFDUixTQUFTRCxJQUFJLENBQUNTLEtBQUssSUFBSTtZQUNyQztRQUNGLEVBQUUsT0FBT0EsT0FBWTtnQkFDUEEsc0JBQUFBO1lBQVp2Qyx1REFBS0EsQ0FBQ3VDLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1SLFFBQVEsY0FBZFEsdUNBQUFBLHVCQUFBQSxnQkFBZ0JULElBQUksY0FBcEJTLDJDQUFBQSxxQkFBc0JBLEtBQUssS0FBSTtRQUM3QztJQUNGO0lBRUEsYUFBYTtJQUNiLE1BQU1VLFlBQVk7UUFDaEIsSUFBSXpCLFlBQVksR0FBRztRQUVuQixJQUFJO1lBQ0YsTUFBTU8sV0FBVyxNQUFNMUIseUNBQUdBLENBQUMyQixJQUFJLENBQUMsc0JBQXNCO2dCQUFFeEI7WUFBTztZQUUvRCxJQUFJdUIsU0FBU0QsSUFBSSxDQUFDSSxPQUFPLEVBQUU7Z0JBQ3pCVCxhQUFhO2dCQUNiLE1BQU1VLFFBQVFDLFlBQVk7b0JBQ3hCWCxhQUFhLENBQUNZO3dCQUNaLElBQUlBLFFBQVEsR0FBRzs0QkFDYkMsY0FBY0g7NEJBQ2QsT0FBTzt3QkFDVDt3QkFDQSxPQUFPRSxPQUFPO29CQUNoQjtnQkFDRixHQUFHO2dCQUVIckMsdURBQUtBLENBQUNrQyxPQUFPLENBQUM7WUFDaEI7UUFDRixFQUFFLE9BQU9LLE9BQVk7WUFDbkJ2Qyx1REFBS0EsQ0FBQ3VDLEtBQUssQ0FBQztRQUNkO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ1c7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTtvQkFBbUJDLE9BQU87d0JBQ3ZDQyxpQkFBa0I7d0JBQ2xCQyxnQkFBZ0I7b0JBQ2xCOzs7Ozs7Ozs7OzswQkFHRiw4REFBQ0o7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2pELHlJQUFTQTt3Q0FBQ2lELFdBQVU7Ozs7Ozs7Ozs7OzhDQUV2Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNJO3dDQUFLSixXQUFVO2tEQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNckQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0s7Z0NBQUdMLFdBQVU7MENBQ1hsQyxTQUFTLFdBQVcsMEJBQTBCOzs7Ozs7MENBRWpELDhEQUFDd0M7Z0NBQUVOLFdBQVU7MENBQ1ZsQyxTQUFTLFdBQ04sNENBQ0EsZUFBc0IsT0FBUFQsUUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1oQyw4REFBQzBDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7d0JBR1psQyxTQUFTLDBCQUNSLDhEQUFDeUM7NEJBQUtQLFdBQVU7NEJBQVlRLFVBQVVqQyxXQUFXa0MsWUFBWSxDQUFDL0I7OzhDQUM1RCw4REFBQ3FCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1U7NENBQU1DLFNBQVE7NENBQVNYLFdBQVU7OzhEQUNoQyw4REFBQ2hELDBJQUFxQkE7b0RBQUNnRCxXQUFVOzs7Ozs7Z0RBQXdCOzs7Ozs7O3NEQUczRCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFDRSxHQUFHckMsV0FBV3NDLFFBQVEsQ0FBQyxTQUFTO29EQUNqQ0MsTUFBSztvREFDTEMsY0FBYTtvREFDYkMsV0FBVztvREFDWGhCLFdBQVcsb0dBSVYsT0FIQ3pCLFdBQVcwQyxTQUFTLENBQUNDLE1BQU0sQ0FBQzdELE1BQU0sR0FDOUIsd0NBQ0EsZ0VBQ0w7b0RBQ0Q4RCxhQUFZOzs7Ozs7OERBRWQsOERBQUNwQjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0k7d0RBQUtKLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FHM0N6QixXQUFXMEMsU0FBUyxDQUFDQyxNQUFNLENBQUM3RCxNQUFNLGtCQUNqQyw4REFBQ2lEOzRDQUFFTixXQUFVOzs4REFDWCw4REFBQ0k7b0RBQUtKLFdBQVU7OERBQU87Ozs7OztnREFDdEJ6QixXQUFXMEMsU0FBUyxDQUFDQyxNQUFNLENBQUM3RCxNQUFNLENBQUMrRCxPQUFPOzs7Ozs7Ozs7Ozs7OzhDQUtqRCw4REFBQ3JCOzhDQUNDLDRFQUFDc0I7d0NBQ0NQLE1BQUs7d0NBQ0xRLFVBQVV6RDt3Q0FDVm1DLFdBQVU7a0RBRVRuQywwQkFDQyw4REFBQ2tDOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7OztnREFBc0Y7Ozs7OztpRUFJdkcsOERBQUNJOzRDQUFLSixXQUFVOzs4REFDZCw4REFBQ2hELDBJQUFxQkE7b0RBQUNnRCxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFVN0RsQyxTQUFTLHVCQUNSLDhEQUFDeUM7NEJBQUtQLFdBQVU7NEJBQVlRLFVBQVUvQixRQUFRZ0MsWUFBWSxDQUFDcEI7OzhDQUN6RCw4REFBQ1U7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDVTs0Q0FBTUMsU0FBUTs0Q0FBTVgsV0FBVTs7OERBQzdCLDhEQUFDL0MsMElBQWVBO29EQUFDK0MsV0FBVTs7Ozs7O2dEQUF3Qjs7Ozs7OztzREFHckQsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDWTtnREFDRSxHQUFHbkMsUUFBUW9DLFFBQVEsQ0FBQyxNQUFNO2dEQUMzQkMsTUFBSztnREFDTEUsV0FBVztnREFDWGhCLFdBQVcsb0dBSVYsT0FIQ3ZCLFFBQVF3QyxTQUFTLENBQUNDLE1BQU0sQ0FBQ3hELEdBQUcsR0FDeEIsd0NBQ0EsZ0VBQ0w7Z0RBQ0R5RCxhQUFZOzs7Ozs7Ozs7Ozt3Q0FHZjFDLFFBQVF3QyxTQUFTLENBQUNDLE1BQU0sQ0FBQ3hELEdBQUcsa0JBQzNCLDhEQUFDNEM7NENBQUVOLFdBQVU7OzhEQUNYLDhEQUFDSTtvREFBS0osV0FBVTs4REFBTzs7Ozs7O2dEQUN0QnZCLFFBQVF3QyxTQUFTLENBQUNDLE1BQU0sQ0FBQ3hELEdBQUcsQ0FBQzBELE9BQU87Ozs7Ozs7Ozs7Ozs7Z0NBTTFDL0MsWUFBWSxtQkFDWCw4REFBQzBCO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDTTt3Q0FBRU4sV0FBVTs7NENBQXdCOzBEQUNyQiw4REFBQ0k7Z0RBQUtKLFdBQVU7O29EQUFnQ3VCLEtBQUtDLEtBQUssQ0FBQ25ELFlBQVk7b0RBQUk7b0RBQUdBLENBQUFBLFlBQVksRUFBQyxFQUFHb0QsUUFBUSxHQUFHQyxRQUFRLENBQUMsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUt6SSw4REFBQzNCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3FCOzRDQUNDUCxNQUFLOzRDQUNMUSxVQUFVekQ7NENBQ1ZtQyxXQUFVO3NEQUVUbkMsMEJBQ0MsOERBQUNrQztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzs7Ozs7b0RBQXNGOzs7Ozs7cUVBSXZHLDhEQUFDSTtnREFBS0osV0FBVTs7a0VBQ2QsOERBQUMvQywwSUFBZUE7d0RBQUMrQyxXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7Ozs7Ozs7c0RBT2xELDhEQUFDcUI7NENBQ0NQLE1BQUs7NENBQ0xhLFNBQVM3Qjs0Q0FDVHdCLFVBQVVqRCxZQUFZOzRDQUN0QjJCLFdBQVU7c0RBRVQzQixZQUFZLElBQUksbUJBQW1COzs7Ozs7c0RBSXRDLDhEQUFDZ0Q7NENBQ0NQLE1BQUs7NENBQ0xhLFNBQVM7Z0RBQ1A1RCxRQUFRO2dEQUNSTyxhQUFhO2dEQUNiRyxRQUFRbUQsS0FBSzs0Q0FDZjs0Q0FDQTVCLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVdYLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDSTt3Q0FBS0osV0FBVTtrREFBK0I7Ozs7Ozs7Ozs7OzhDQUVqRCw4REFBQzZCO29DQUFHN0IsV0FBVTs4Q0FBa0M7Ozs7Ozs7Ozs7OztzQ0FFbEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBRU4sV0FBVTtzREFBMkM7Ozs7OztzREFDeEQsOERBQUNNOzRDQUFFTixXQUFVO3NEQUFrQzs7Ozs7Ozs7Ozs7OzhDQUVqRCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBRU4sV0FBVTtzREFBMkM7Ozs7OztzREFDeEQsOERBQUNNOzRDQUFFTixXQUFVO3NEQUFrQzs7Ozs7Ozs7Ozs7OzhDQUVqRCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBRU4sV0FBVTtzREFBMkM7Ozs7OztzREFDeEQsOERBQUNNOzRDQUFFTixXQUFVO3NEQUFrQzs7Ozs7Ozs7Ozs7OzhDQUVqRCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBRU4sV0FBVTtzREFBMkM7Ozs7OztzREFDeEQsOERBQUNNOzRDQUFFTixXQUFVO3NEQUFrQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtuRCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FBRU4sV0FBVTs4Q0FBMkM7Ozs7Ozs4Q0FDeEQsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaO3dDQUFDO3dDQUFVO3dDQUFVO3dDQUFVO3dDQUFVO3FDQUFTLENBQUM4QixHQUFHLENBQUMsQ0FBQ3BFLG9CQUN2RCw4REFBQ3FDOzRDQUFjQyxXQUFVO3NEQUN2Qiw0RUFBQ007Z0RBQUVOLFdBQVU7MERBQW1DdEM7Ozs7OzsyQ0FEeENBOzs7Ozs7Ozs7OzhDQUtkLDhEQUFDNEM7b0NBQUVOLFdBQVU7OENBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF0RDtHQTNVd0JyQzs7UUFDUGxCLHNEQUFTQTtRQUNGSywwREFBWUE7UUFRZkosb0RBQU9BO1FBS1ZBLG9EQUFPQTs7O0tBZkRpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2F1dGgvbG9naW4vcGFnZS50c3g/ZjZmMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZUZvcm0gfSBmcm9tICdyZWFjdC1ob29rLWZvcm0nO1xuaW1wb3J0IHsgem9kUmVzb2x2ZXIgfSBmcm9tICdAaG9va2Zvcm0vcmVzb2x2ZXJzL3pvZCc7XG5pbXBvcnQgeyB6IH0gZnJvbSAnem9kJztcbmltcG9ydCB0b2FzdCBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuaW1wb3J0IHsgdXNlQXV0aFN0b3JlIH0gZnJvbSAnQC9zdG9yZS9hdXRoU3RvcmUnO1xuaW1wb3J0IHsgVHJ1Y2tJY29uLCBEZXZpY2VQaG9uZU1vYmlsZUljb24sIFNoaWVsZENoZWNrSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyBhcGkgfSBmcm9tICdAL2xpYi9hcGknO1xuXG4vLyBTTVMgT1RQIExvZ2luIFNjaGVtYSAoTW9iaWxlLW9ubHkgbG9naW4gYXMgcGVyIGxlZ2FjeSBzeXN0ZW0pXG5jb25zdCBtb2JpbGVMb2dpblNjaGVtYSA9IHoub2JqZWN0KHtcbiAgbW9iaWxlOiB6LnN0cmluZygpXG4gICAgLnJlZ2V4KC9eWzYtOV1cXGR7OX0kLywgJ01vYmlsZSBudW1iZXIgbXVzdCBiZSAxMCBkaWdpdHMgc3RhcnRpbmcgd2l0aCA2LTknKVxuICAgIC5sZW5ndGgoMTAsICdNb2JpbGUgbnVtYmVyIG11c3QgYmUgZXhhY3RseSAxMCBkaWdpdHMnKSxcbn0pO1xuXG4vLyBPVFAgVmVyaWZpY2F0aW9uIFNjaGVtYVxuY29uc3Qgb3RwVmVyaWZpY2F0aW9uU2NoZW1hID0gei5vYmplY3Qoe1xuICBvdHA6IHouc3RyaW5nKClcbiAgICAubGVuZ3RoKDYsICdPVFAgbXVzdCBiZSBleGFjdGx5IDYgZGlnaXRzJylcbiAgICAucmVnZXgoL15cXGR7Nn0kLywgJ09UUCBtdXN0IGNvbnRhaW4gb25seSBudW1iZXJzJyksXG59KTtcblxudHlwZSBNb2JpbGVMb2dpbkRhdGEgPSB6LmluZmVyPHR5cGVvZiBtb2JpbGVMb2dpblNjaGVtYT47XG50eXBlIE90cFZlcmlmaWNhdGlvbkRhdGEgPSB6LmluZmVyPHR5cGVvZiBvdHBWZXJpZmljYXRpb25TY2hlbWE+O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2dpblBhZ2UoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCB7IGlzTG9hZGluZyB9ID0gdXNlQXV0aFN0b3JlKCk7XG4gIGNvbnN0IFtzdGVwLCBzZXRTdGVwXSA9IHVzZVN0YXRlPCdtb2JpbGUnIHwgJ290cCc+KCdtb2JpbGUnKTtcbiAgY29uc3QgW21vYmlsZSwgc2V0TW9iaWxlXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3VzZXJJZCwgc2V0VXNlcklkXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbb3RwU2VudCwgc2V0T3RwU2VudF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjb3VudGRvd24sIHNldENvdW50ZG93bl0gPSB1c2VTdGF0ZSgwKTtcblxuICAvLyBNb2JpbGUgbG9naW4gZm9ybVxuICBjb25zdCBtb2JpbGVGb3JtID0gdXNlRm9ybTxNb2JpbGVMb2dpbkRhdGE+KHtcbiAgICByZXNvbHZlcjogem9kUmVzb2x2ZXIobW9iaWxlTG9naW5TY2hlbWEpLFxuICB9KTtcblxuICAvLyBPVFAgdmVyaWZpY2F0aW9uIGZvcm1cbiAgY29uc3Qgb3RwRm9ybSA9IHVzZUZvcm08T3RwVmVyaWZpY2F0aW9uRGF0YT4oe1xuICAgIHJlc29sdmVyOiB6b2RSZXNvbHZlcihvdHBWZXJpZmljYXRpb25TY2hlbWEpLFxuICB9KTtcblxuICAvLyBTZW5kIE9UUFxuICBjb25zdCBvbk1vYmlsZVN1Ym1pdCA9IGFzeW5jIChkYXRhOiBNb2JpbGVMb2dpbkRhdGEpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucG9zdCgnL2F1dGgvYnlwYXNzL2dldC1vdHAnLCB7IGxvZ2luX21vYmlsZTogZGF0YS5tb2JpbGUgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0TW9iaWxlKGRhdGEubW9iaWxlKTtcbiAgICAgICAgc2V0VXNlcklkKHJlc3BvbnNlLmRhdGEuZGF0YS51c2VySWQpO1xuICAgICAgICBzZXRTdGVwKCdvdHAnKTtcbiAgICAgICAgc2V0T3RwU2VudCh0cnVlKTtcbiAgICAgICAgc2V0Q291bnRkb3duKDEyMCk7IC8vIDIgbWludXRlcyBjb3VudGRvd25cblxuICAgICAgICAvLyBTdGFydCBjb3VudGRvd24gdGltZXJcbiAgICAgICAgY29uc3QgdGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICAgICAgc2V0Q291bnRkb3duKChwcmV2KSA9PiB7XG4gICAgICAgICAgICBpZiAocHJldiA8PSAxKSB7XG4gICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwodGltZXIpO1xuICAgICAgICAgICAgICByZXR1cm4gMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBwcmV2IC0gMTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSwgMTAwMCk7XG5cbiAgICAgICAgdG9hc3Quc3VjY2VzcygnT1RQIHNlbnQgdG8geW91ciBtb2JpbGUgbnVtYmVyIScpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdG9hc3QuZXJyb3IocmVzcG9uc2UuZGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIHNlbmQgT1RQJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/LmVycm9yIHx8ICdGYWlsZWQgdG8gc2VuZCBPVFAnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gVmVyaWZ5IE9UUFxuICBjb25zdCBvbk90cFN1Ym1pdCA9IGFzeW5jIChkYXRhOiBPdHBWZXJpZmljYXRpb25EYXRhKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoJy9hdXRoL3ZlcmlmeS1vdHAnLCB7XG4gICAgICAgIG1vYmlsZSxcbiAgICAgICAgb3RwOiBkYXRhLm90cFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgY29uc3QgeyB0b2tlbnMsIHVzZXIgfSA9IHJlc3BvbnNlLmRhdGEuZGF0YTtcblxuICAgICAgICAvLyBVcGRhdGUgYXV0aCBzdG9yZSBzdGF0ZSAodGhpcyB3aWxsIGFsc28gaGFuZGxlIGxvY2FsU3RvcmFnZS9jb29raWVzKVxuICAgICAgICBjb25zdCBhdXRoU3RvcmUgPSB1c2VBdXRoU3RvcmUuZ2V0U3RhdGUoKTtcbiAgICAgICAgYXV0aFN0b3JlLnNtc090cExvZ2luKHVzZXIsIHRva2Vucy5hY2Nlc3NUb2tlbiwgdG9rZW5zLnJlZnJlc2hUb2tlbik7XG5cbiAgICAgICAgdG9hc3Quc3VjY2VzcygnTG9naW4gc3VjY2Vzc2Z1bCEnKTtcblxuICAgICAgICAvLyBSZWRpcmVjdCB0byBtYWluIGRhc2hib2FyZCAoYWxsIHVzZXIgdHlwZXMgdXNlIHRoZSBzYW1lIGRhc2hib2FyZClcbiAgICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRvYXN0LmVycm9yKHJlc3BvbnNlLmRhdGEuZXJyb3IgfHwgJ0ludmFsaWQgT1RQJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/LmVycm9yIHx8ICdPVFAgdmVyaWZpY2F0aW9uIGZhaWxlZCcpO1xuICAgIH1cbiAgfTtcblxuICAvLyBSZXNlbmQgT1RQXG4gIGNvbnN0IHJlc2VuZE90cCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoY291bnRkb3duID4gMCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoJy9hdXRoL21vYmlsZS1sb2dpbicsIHsgbW9iaWxlIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldENvdW50ZG93bigxMjApO1xuICAgICAgICBjb25zdCB0aW1lciA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgICBzZXRDb3VudGRvd24oKHByZXYpID0+IHtcbiAgICAgICAgICAgIGlmIChwcmV2IDw9IDEpIHtcbiAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbCh0aW1lcik7XG4gICAgICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHByZXYgLSAxO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9LCAxMDAwKTtcblxuICAgICAgICB0b2FzdC5zdWNjZXNzKCdPVFAgcmVzZW50IHN1Y2Nlc3NmdWxseSEnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIHJlc2VuZCBPVFAnKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTUwIHRvLWJsdWUtNTAgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LWNlbnRlciBweS0xMiBzbTpweC02IGxnOnB4LTggcmVsYXRpdmVcIj5cbiAgICAgIHsvKiBCYWNrZ3JvdW5kIFBhdHRlcm4gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgb3BhY2l0eS00MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTBcIiBzdHlsZT17e1xuICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0Nzdmcgd2lkdGg9JzYwJyBoZWlnaHQ9JzYwJyB2aWV3Qm94PScwIDAgNjAgNjAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyclM0UlM0NnIGZpbGw9J25vbmUnIGZpbGwtcnVsZT0nZXZlbm9kZCclM0UlM0NnIGZpbGw9JyUyM2UyZThmMCcgZmlsbC1vcGFjaXR5PScwLjQnJTNFJTNDY2lyY2xlIGN4PSc3JyBjeT0nNycgcj0nMScvJTNFJTNDL2clM0UlM0MvZyUzRSUzQy9zdmclM0VcIilgLFxuICAgICAgICAgIGJhY2tncm91bmRTaXplOiAnNjBweCA2MHB4J1xuICAgICAgICB9fT48L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHNtOm14LWF1dG8gc206dy1mdWxsIHNtOm1heC13LW1kXCI+XG4gICAgICAgIHsvKiBMb2dvIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTE2IHctMTYgcm91bmRlZC0yeGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1bIzBlMmQ2N10gdG8tWyMxYTQ0ODBdIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy14bFwiPlxuICAgICAgICAgICAgICA8VHJ1Y2tJY29uIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIC1yaWdodC0xIGgtNiB3LTYgcm91bmRlZC1mdWxsIGJnLVsjZTMwMDBmXSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXhzXCI+RVA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICB7c3RlcCA9PT0gJ21vYmlsZScgPyAnV2VsY29tZSB0byBlUGFyaXZhaGFuJyA6ICdWZXJpZnkgT1RQJ31cbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICAgICAge3N0ZXAgPT09ICdtb2JpbGUnXG4gICAgICAgICAgICAgID8gJ0VudGVyIHlvdXIgbW9iaWxlIG51bWJlciB0byByZWNlaXZlIE9UUCdcbiAgICAgICAgICAgICAgOiBgT1RQIHNlbnQgdG8gJHttb2JpbGV9LiBFbnRlciB0aGUgNi1kaWdpdCBjb2RlLmBcbiAgICAgICAgICAgIH1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbXQtOCBzbTpteC1hdXRvIHNtOnctZnVsbCBzbTptYXgtdy1tZFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0yeGwgc2hhZG93LTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHAtOFwiPlxuXG4gICAgICAgICAgey8qIE1vYmlsZSBOdW1iZXIgU3RlcCAqL31cbiAgICAgICAgICB7c3RlcCA9PT0gJ21vYmlsZScgJiYgKFxuICAgICAgICAgICAgPGZvcm0gY2xhc3NOYW1lPVwic3BhY2UteS02XCIgb25TdWJtaXQ9e21vYmlsZUZvcm0uaGFuZGxlU3VibWl0KG9uTW9iaWxlU3VibWl0KX0+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJtb2JpbGVcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPERldmljZVBob25lTW9iaWxlSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IGlubGluZSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIE1vYmlsZSBOdW1iZXJcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB7Li4ubW9iaWxlRm9ybS5yZWdpc3RlcignbW9iaWxlJyl9XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZWxcIlxuICAgICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9XCJ0ZWxcIlxuICAgICAgICAgICAgICAgICAgICBtYXhMZW5ndGg9ezEwfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtNCBweS0zIHJvdW5kZWQteGwgYm9yZGVyLTIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTAgJHtcbiAgICAgICAgICAgICAgICAgICAgICBtb2JpbGVGb3JtLmZvcm1TdGF0ZS5lcnJvcnMubW9iaWxlXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcmVkLTMwMCBmb2N1czpib3JkZXItcmVkLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBmb2N1czpib3JkZXItWyMwZTJkNjddIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgICAgfSBiZy13aGl0ZS81MCBiYWNrZHJvcC1ibHVyLXNtIHRleHQtbGcgdHJhY2tpbmctd2lkZXJgfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjk4NzY1NDMyMTBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQteS0wIGxlZnQtMCBwbC0zIGZsZXggaXRlbXMtY2VudGVyIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtXCI+KzkxPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAge21vYmlsZUZvcm0uZm9ybVN0YXRlLmVycm9ycy5tb2JpbGUgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDAgZmxleCBpdGVtcy1jZW50ZXIgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0xXCI+4pqgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7bW9iaWxlRm9ybS5mb3JtU3RhdGUuZXJyb3JzLm1vYmlsZS5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1bIzBlMmQ2N10gdG8tWyMxYTQ0ODBdIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBweS0zIHB4LTQgcm91bmRlZC14bCBob3Zlcjpmcm9tLVsjMWE0NDgwXSBob3Zlcjp0by1bIzI2NTlhM10gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctNCBmb2N1czpyaW5nLVsjMGUyZDY3XS8yMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2Zvcm0gaG92ZXI6LXRyYW5zbGF0ZS15LTAuNVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTUgdy01IGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBTZW5kaW5nIE9UUC4uLlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPERldmljZVBob25lTW9iaWxlSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIFNlbmQgT1RQXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogT1RQIFZlcmlmaWNhdGlvbiBTdGVwICovfVxuICAgICAgICAgIHtzdGVwID09PSAnb3RwJyAmJiAoXG4gICAgICAgICAgICA8Zm9ybSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIiBvblN1Ym1pdD17b3RwRm9ybS5oYW5kbGVTdWJtaXQob25PdHBTdWJtaXQpfT5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cIm90cFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICA8U2hpZWxkQ2hlY2tJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgaW5saW5lIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgRW50ZXIgT1RQXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgey4uLm90cEZvcm0ucmVnaXN0ZXIoJ290cCcpfVxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIG1heExlbmd0aD17Nn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTQgcHktMyByb3VuZGVkLXhsIGJvcmRlci0yIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0wICR7XG4gICAgICAgICAgICAgICAgICAgICAgb3RwRm9ybS5mb3JtU3RhdGUuZXJyb3JzLm90cFxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLXJlZC0zMDAgZm9jdXM6Ym9yZGVyLXJlZC01MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgZm9jdXM6Ym9yZGVyLVsjMGUyZDY3XSBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICAgIH0gYmctd2hpdGUvNTAgYmFja2Ryb3AtYmx1ci1zbSB0ZXh0LWNlbnRlciB0ZXh0LTJ4bCB0cmFja2luZy13aWRlc3QgZm9udC1tb25vYH1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwMDAwMDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICB7b3RwRm9ybS5mb3JtU3RhdGUuZXJyb3JzLm90cCAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMCBmbGV4IGl0ZW1zLWNlbnRlciBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTFcIj7imqA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIHtvdHBGb3JtLmZvcm1TdGF0ZS5lcnJvcnMub3RwLm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIENvdW50ZG93biBUaW1lciAqL31cbiAgICAgICAgICAgICAge2NvdW50ZG93biA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICBSZXNlbmQgT1RQIGluIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1bIzBlMmQ2N11cIj57TWF0aC5mbG9vcihjb3VudGRvd24gLyA2MCl9OnsoY291bnRkb3duICUgNjApLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLVsjMGUyZDY3XSB0by1bIzFhNDQ4MF0gdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIHB5LTMgcHgtNCByb3VuZGVkLXhsIGhvdmVyOmZyb20tWyMxYTQ0ODBdIGhvdmVyOnRvLVsjMjY1OWEzXSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy00IGZvY3VzOnJpbmctWyMwZTJkNjddLzIwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41XCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLTIgYm9yZGVyLXdoaXRlIGJvcmRlci10LXRyYW5zcGFyZW50IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIFZlcmlmeWluZy4uLlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNoaWVsZENoZWNrSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIFZlcmlmeSAmIExvZ2luXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICB7LyogUmVzZW5kIE9UUCBCdXR0b24gKi99XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtyZXNlbmRPdHB9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y291bnRkb3duID4gMH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXItMiBib3JkZXItZ3JheS0yMDAgdGV4dC1ncmF5LTcwMCBmb250LXNlbWlib2xkIHB5LTIgcHgtNCByb3VuZGVkLXhsIGhvdmVyOmJvcmRlci1bIzBlMmQ2N10gaG92ZXI6dGV4dC1bIzBlMmQ2N10gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctNCBmb2N1czpyaW5nLVsjMGUyZDY3XS8yMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgYmctd2hpdGUvNTAgYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2NvdW50ZG93biA+IDAgPyAnUGxlYXNlIHdhaXQuLi4nIDogJ1Jlc2VuZCBPVFAnfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIEJhY2sgdG8gTW9iaWxlIFN0ZXAgKi99XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHNldFN0ZXAoJ21vYmlsZScpO1xuICAgICAgICAgICAgICAgICAgICBzZXRDb3VudGRvd24oMCk7XG4gICAgICAgICAgICAgICAgICAgIG90cEZvcm0ucmVzZXQoKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1ncmF5LTYwMCBmb250LW1lZGl1bSBweS0yIHB4LTQgcm91bmRlZC14bCBob3Zlcjp0ZXh0LVsjMGUyZDY3XSBmb2N1czpvdXRsaW5lLW5vbmUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDihpAgQ2hhbmdlIE1vYmlsZSBOdW1iZXJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgKX1cblxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRGVtbyBDcmVkZW50aWFscyAmIERlZmF1bHQgT1RQcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbXQtOCBzbTpteC1hdXRvIHNtOnctZnVsbCBzbTptYXgtdy1tZFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby01MCBib3JkZXItMiBib3JkZXItYmx1ZS0xMDAgcm91bmRlZC0yeGwgcC02IHNoYWRvdy1sZ1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggdy04IHJvdW5kZWQtbGcgYmctYmx1ZS01MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXItM1wiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXNtXCI+8J+UkTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtYmx1ZS05MDBcIj5EZW1vIExvZ2luIERldGFpbHM8L2gzPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzYwIHJvdW5kZWQtbGcgcC0zIGJvcmRlciBib3JkZXItYmx1ZS0yMDBcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtYmx1ZS04MDAgbWItMVwiPkFkbWluIFVzZXI8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTkwMCBmb250LW1vbm9cIj7wn5OxIDgyNDAzMDE4OTU8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNjAgcm91bmRlZC1sZyBwLTMgYm9yZGVyIGJvcmRlci1ibHVlLTIwMFwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTgwMCBtYi0xXCI+Q29uc2lnbmVlIChMb2FkIFBvc3Rlcik8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTkwMCBmb250LW1vbm9cIj7wn5OxIDk4NzQ4OTY5MDA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNjAgcm91bmRlZC1sZyBwLTMgYm9yZGVyIGJvcmRlci1ibHVlLTIwMFwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTgwMCBtYi0xXCI+RmxlZXQgT3duZXIgKFRyYW5zcG9ydGVyKTwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtOTAwIGZvbnQtbW9ub1wiPvCfk7EgODE4MTgxNjQ3NzwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS82MCByb3VuZGVkLWxnIHAtMyBib3JkZXIgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtODAwIG1iLTFcIj5GbGVldCBPd25lciAoVHJhbnNwb3J0ZXIpPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS05MDAgZm9udC1tb25vXCI+8J+TsSA5MDUxNzIwMTMzPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGVmYXVsdCBPVFBzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwdC00IGJvcmRlci10IGJvcmRlci1ibHVlLTIwMFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtYmx1ZS04MDAgbWItMlwiPkRlZmF1bHQgT1RQcyAoQWRtaW4gQWNjZXNzKTwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNSBnYXAtMlwiPlxuICAgICAgICAgICAgICB7WycxMjM0NTYnLCAnMTExMTExJywgJzAwMDAwMCcsICc5OTk5OTknLCAnNTU1NTU1J10ubWFwKChvdHApID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17b3RwfSBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLWxnIHAtMiB0ZXh0LWNlbnRlciBib3JkZXIgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbW9ubyB0ZXh0LWJsdWUtOTAwXCI+e290cH08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgbXQtMlwiPlxuICAgICAgICAgICAgICBUaGVzZSBPVFBzIHdvcmsgZm9yIGFueSBtb2JpbGUgbnVtYmVyIGZvciB0ZXN0aW5nIHB1cnBvc2VzLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUm91dGVyIiwidXNlRm9ybSIsInpvZFJlc29sdmVyIiwieiIsInRvYXN0IiwidXNlQXV0aFN0b3JlIiwiVHJ1Y2tJY29uIiwiRGV2aWNlUGhvbmVNb2JpbGVJY29uIiwiU2hpZWxkQ2hlY2tJY29uIiwiYXBpIiwibW9iaWxlTG9naW5TY2hlbWEiLCJvYmplY3QiLCJtb2JpbGUiLCJzdHJpbmciLCJyZWdleCIsImxlbmd0aCIsIm90cFZlcmlmaWNhdGlvblNjaGVtYSIsIm90cCIsIkxvZ2luUGFnZSIsInJvdXRlciIsImlzTG9hZGluZyIsInN0ZXAiLCJzZXRTdGVwIiwic2V0TW9iaWxlIiwidXNlcklkIiwic2V0VXNlcklkIiwib3RwU2VudCIsInNldE90cFNlbnQiLCJjb3VudGRvd24iLCJzZXRDb3VudGRvd24iLCJtb2JpbGVGb3JtIiwicmVzb2x2ZXIiLCJvdHBGb3JtIiwib25Nb2JpbGVTdWJtaXQiLCJkYXRhIiwicmVzcG9uc2UiLCJwb3N0IiwibG9naW5fbW9iaWxlIiwic3VjY2VzcyIsInRpbWVyIiwic2V0SW50ZXJ2YWwiLCJwcmV2IiwiY2xlYXJJbnRlcnZhbCIsImVycm9yIiwib25PdHBTdWJtaXQiLCJ0b2tlbnMiLCJ1c2VyIiwiYXV0aFN0b3JlIiwiZ2V0U3RhdGUiLCJzbXNPdHBMb2dpbiIsImFjY2Vzc1Rva2VuIiwicmVmcmVzaFRva2VuIiwicHVzaCIsInJlc2VuZE90cCIsImRpdiIsImNsYXNzTmFtZSIsInN0eWxlIiwiYmFja2dyb3VuZEltYWdlIiwiYmFja2dyb3VuZFNpemUiLCJzcGFuIiwiaDEiLCJwIiwiZm9ybSIsIm9uU3VibWl0IiwiaGFuZGxlU3VibWl0IiwibGFiZWwiLCJodG1sRm9yIiwiaW5wdXQiLCJyZWdpc3RlciIsInR5cGUiLCJhdXRvQ29tcGxldGUiLCJtYXhMZW5ndGgiLCJmb3JtU3RhdGUiLCJlcnJvcnMiLCJwbGFjZWhvbGRlciIsIm1lc3NhZ2UiLCJidXR0b24iLCJkaXNhYmxlZCIsIk1hdGgiLCJmbG9vciIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJvbkNsaWNrIiwicmVzZXQiLCJoMyIsIm1hcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ })

});