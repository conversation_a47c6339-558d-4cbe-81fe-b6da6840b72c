"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { smsOtpLogin } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // 1: mobile, 2: otp, 3: success\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        login_mobile: \"\",\n        user_otp: \"\",\n        user_id: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Step 1: Send OTP\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/auth/bypass/get-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    login_mobile: formData.login_mobile\n                })\n            });\n            const data = await response.json();\n            if (data.alert === \"success\") {\n                setFormData((prev)=>({\n                        ...prev,\n                        user_id: data.user_id\n                    }));\n                setStep(2);\n            } else {\n                setError(data.message || \"Failed to send OTP\");\n            }\n        } catch (err) {\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Step 2: Verify OTP\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"Sending OTP verification request:\", {\n                user_id: formData.user_id,\n                user_otp: formData.user_otp\n            });\n            const response = await fetch(\"/api/auth/bypass/login-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    user_id: formData.user_id,\n                    user_otp: formData.user_otp\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            const data = await response.json();\n            console.log(\"Response data:\", data);\n            console.log(\"=== AUTHENTICATION RESPONSE ===\");\n            console.log(\"Full response data:\", data);\n            console.log(\"Alert status:\", data.alert);\n            console.log(\"Redirect URL:\", data.redirectUrl);\n            console.log(\"================================\");\n            if (data.alert === \"success\") {\n                console.log(\"✅ Authentication successful!\");\n                // Store all data immediately\n                localStorage.setItem(\"token\", data.token);\n                localStorage.setItem(\"user\", JSON.stringify(data.user));\n                localStorage.setItem(\"isAuthenticated\", \"true\");\n                localStorage.setItem(\"tempUser\", JSON.stringify(data.user));\n                localStorage.setItem(\"pendingRedirectUrl\", data.redirectUrl || \"/dashboard\");\n                // Show success step with immediate redirect options\n                setStep(3);\n                // Try multiple redirect methods\n                console.log(\"\\uD83D\\uDD04 Attempting multiple redirect methods...\");\n                // Method 1: Immediate redirect\n                try {\n                    window.location.href = data.redirectUrl || \"/dashboard\";\n                } catch (e) {\n                    console.log(\"Method 1 failed:\", e);\n                }\n                // Method 2: Replace location\n                setTimeout(()=>{\n                    try {\n                        window.location.replace(data.redirectUrl || \"/dashboard\");\n                    } catch (e) {\n                        console.log(\"Method 2 failed:\", e);\n                    }\n                }, 500);\n                // Method 3: Assign location\n                setTimeout(()=>{\n                    try {\n                        window.location.assign(data.redirectUrl || \"/dashboard\");\n                    } catch (e) {\n                        console.log(\"Method 3 failed:\", e);\n                    }\n                }, 1000);\n            } else {\n                console.log(\"❌ Authentication failed:\", data);\n                setError(data.message || \"Invalid OTP\");\n            }\n        } catch (err) {\n            console.error(\"Network error during OTP verification:\", err);\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#0e2d67] via-[#1e3a8a] to-[#e3000f] flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-16 h-16 bg-gradient-to-r from-[#0e2d67] to-[#e3000f] rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"eParivahan Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: step === 1 ? \"Enter your mobile number\" : step === 2 ? \"Enter the OTP sent to your mobile\" : \"Login successful! Redirecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this),\n                step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3\",\n                                children: \"✅ Login successful! Redirecting...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 text-sm\",\n                                children: \"Check browser console for redirect details.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log(\"Manual redirect to auth-success clicked\");\n                                            const redirectUrl = localStorage.getItem(\"pendingRedirectUrl\") || \"/auth-success\";\n                                            window.location.href = redirectUrl;\n                                        },\n                                        className: \"block w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: \"Go to Auth Success Page\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log(\"Manual redirect to dashboard clicked\");\n                                            window.location.href = \"/dashboard\";\n                                        },\n                                        className: \"block w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: \"Go to Dashboard Directly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false),\n                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Mobile Number \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 31\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: formData.login_mobile,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        login_mobile: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                            placeholder: \"Enter 10-digit mobile number\",\n                                            pattern: \"[6-9][0-9]{9}\",\n                                            maxLength: \"10\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: \"Enter mobile number starting with 6, 7, 8, or 9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading || formData.login_mobile.length !== 10,\n                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Sending OTP...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this) : \"Send OTP\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 11\n                }, this),\n                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleVerifyOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Enter OTP \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.user_otp,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        user_otp: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent text-center text-lg tracking-widest\",\n                                            placeholder: \"000000\",\n                                            maxLength: \"6\",\n                                            pattern: \"[0-9]{6}\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: [\n                                        \"OTP sent to +91 \",\n                                        formData.login_mobile\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setStep(1),\n                                    className: \"flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg hover:bg-gray-200 transition-colors\",\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || formData.user_otp.length !== 6,\n                                    className: \"flex-1 bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Verifying...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Login\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 eParivahan. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"LVXK5e1QL+GynZVgxj92VNUDRVI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ })

});