"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* harmony import */ var _store_dashboardStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/dashboardStore */ \"(app-pages-browser)/./src/store/dashboardStore.ts\");\n/* harmony import */ var _store_socketStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/socketStore */ \"(app-pages-browser)/./src/store/socketStore.ts\");\n/* harmony import */ var _components_dashboard_DashboardHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DashboardHeader */ \"(app-pages-browser)/./src/components/dashboard/DashboardHeader.tsx\");\n/* harmony import */ var _components_dashboard_DashboardSummary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/DashboardSummary */ \"(app-pages-browser)/./src/components/dashboard/DashboardSummary.tsx\");\n/* harmony import */ var _components_dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/QuickActions */ \"(app-pages-browser)/./src/components/dashboard/QuickActions.tsx\");\n/* harmony import */ var _components_dashboard_ActivityFeed__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/ActivityFeed */ \"(app-pages-browser)/./src/components/dashboard/ActivityFeed.tsx\");\n/* harmony import */ var _components_dashboard_LoadAnalysis__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/LoadAnalysis */ \"(app-pages-browser)/./src/components/dashboard/LoadAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_BidAnalysis__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/BidAnalysis */ \"(app-pages-browser)/./src/components/dashboard/BidAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_TransporterParticipationAnalysis__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/TransporterParticipationAnalysis */ \"(app-pages-browser)/./src/components/dashboard/TransporterParticipationAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_TransporterAnalysis__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dashboard/TransporterAnalysis */ \"(app-pages-browser)/./src/components/dashboard/TransporterAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_CancellationSurvey__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dashboard/CancellationSurvey */ \"(app-pages-browser)/./src/components/dashboard/CancellationSurvey.tsx\");\n/* harmony import */ var _components_dashboard_BidPriceAnalysis__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/dashboard/BidPriceAnalysis */ \"(app-pages-browser)/./src/components/dashboard/BidPriceAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_PerformanceAnalysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/dashboard/PerformanceAnalysis */ \"(app-pages-browser)/./src/components/dashboard/PerformanceAnalysis.tsx\");\n/* harmony import */ var _components_dashboard_SalesTrend__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/dashboard/SalesTrend */ \"(app-pages-browser)/./src/components/dashboard/SalesTrend.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isAuthenticated, isLoading, initializeAuth } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const { dashboardData, recentActivity, isLoading: dashboardLoading, initializeRealTimeUpdates, simulateRealTimeUpdates, stopRealTimeUpdates } = (0,_store_dashboardStore__WEBPACK_IMPORTED_MODULE_4__.useDashboardStore)();\n    const { connect, isConnected } = (0,_store_socketStore__WEBPACK_IMPORTED_MODULE_5__.useSocketStore)();\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, [\n        initializeAuth\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated && !isLoading) {\n            console.log(\"Dashboard: User not authenticated, redirecting to login\");\n            router.push(\"/login\"); // Fixed: redirect to correct login page\n            return;\n        } else if (isAuthenticated) {\n            console.log(\"Dashboard: User authenticated, staying on dashboard\");\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        user,\n        router\n    ]);\n    // Real-time updates effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            // Connect to WebSocket for real-time updates\n            connect();\n            // Initialize real-time updates\n            initializeRealTimeUpdates();\n            // Start simulation for demo purposes (remove in production)\n            simulateRealTimeUpdates();\n            // Cleanup on unmount\n            return ()=>{\n                stopRealTimeUpdates();\n            };\n        }\n    }, [\n        user,\n        connect,\n        initializeRealTimeUpdates,\n        simulateRealTimeUpdates,\n        stopRealTimeUpdates\n    ]);\n    // Show loading while checking authentication\n    if (!isAuthenticated || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-4 border-[#0e2d67] border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: isLoading ? \"Loading authentication...\" : \"Checking authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    // Dashboard loading is handled separately and shouldn't block the main dashboard\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardSummary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 flex items-center justify-between bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 w-3 rounded-full \".concat(isConnected ? \"bg-green-400 animate-pulse\" : \"bg-red-400\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: isConnected ? \"Real-time updates active\" : \"Connecting...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                        children: \"Live\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"Last updated: \",\n                                    dashboardData ? \"Just now\" : \"Never\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ActivityFeed__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_LoadAnalysis__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        title: \"Load Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BidAnalysis__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        title: \"Bid Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransporterParticipationAnalysis__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        title: \"Transporter Participation Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransporterAnalysis__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        title: \"Transporter Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CancellationSurvey__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        title: \"Cancellation Survey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BidPriceAnalysis__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        title: \"Bid Price Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_PerformanceAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        title: \"Performance Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SalesTrend__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        title: \"Sales Trend\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Enhanced Logistics Management System\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Real-time analytics and comprehensive dashboard for efficient logistics operations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-6 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 bg-[#0e2d67] rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Primary Color: #0e2d67\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 bg-[#e3000f] rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Secondary Color: #e3000f\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Real-time Updates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"ljo230LBIPL8zc2Fn5nCyxF0sZ0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore,\n        _store_dashboardStore__WEBPACK_IMPORTED_MODULE_4__.useDashboardStore,\n        _store_socketStore__WEBPACK_IMPORTED_MODULE_5__.useSocketStore\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});