'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { useDashboardStore } from '@/store/dashboardStore';
import { useSocketStore } from '@/store/socketStore';
import { dashboardAPI } from '@/lib/api';
import StatsCards from '@/components/dashboard/StatsCards';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSummary from '@/components/dashboard/DashboardSummary';
import QuickActions from '@/components/dashboard/QuickActions';
import ActivityFeed from '@/components/dashboard/ActivityFeed';
import LoadAnalysis from '@/components/dashboard/LoadAnalysis';
import BidAnalysis from '@/components/dashboard/BidAnalysis';
import TransporterParticipationAnalysis from '@/components/dashboard/TransporterParticipationAnalysis';
import TransporterAnalysis from '@/components/dashboard/TransporterAnalysis';
import CancellationSurvey from '@/components/dashboard/CancellationSurvey';
import BidPriceAnalysis from '@/components/dashboard/BidPriceAnalysis';
import PerformanceAnalysis from '@/components/dashboard/PerformanceAnalysis';
import SalesTrend from '@/components/dashboard/SalesTrend';
import {
  TruckIcon,
  CurrencyRupeeIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface DashboardStats {
  totalLoads?: number;
  liveLoads?: number;
  pendingLoads?: number;
  confirmedLoads?: number;
  activeLoads?: number;
  completedLoads?: number;
  totalSpent?: number;
  totalBids?: number;
  wonBids?: number;
  totalEarned?: number;
  totalLoadsTrend?: string;
  liveLoadsTrend?: string;
  pendingLoadsTrend?: string;
  confirmedLoadsTrend?: string;
}

export default function DashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, initializeAuth } = useAuthStore();
  const {
    dashboardData,
    recentActivity,
    isLoading: dashboardLoading,
    initializeRealTimeUpdates,
    simulateRealTimeUpdates,
    stopRealTimeUpdates
  } = useDashboardStore();
  const { connect, isConnected } = useSocketStore();

  // Authentication check
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      console.log('Dashboard: User not authenticated, redirecting to login');
      router.push('/login'); // Fixed: redirect to correct login page
      return;
    } else if (isAuthenticated) {
      console.log('Dashboard: User authenticated, staying on dashboard');
    }
  }, [isAuthenticated, isLoading, user, router]);

  // Real-time updates effect
  useEffect(() => {
    if (user) {
      // Connect to WebSocket for real-time updates
      connect();

      // Initialize real-time updates
      initializeRealTimeUpdates();

      // Start simulation for demo purposes (remove in production)
      simulateRealTimeUpdates();

      // Cleanup on unmount
      return () => {
        stopRealTimeUpdates();
      };
    }
  }, [user, connect, initializeRealTimeUpdates, simulateRealTimeUpdates, stopRealTimeUpdates]);

  // Show loading while checking authentication
  if (!isAuthenticated || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-[#0e2d67] border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">
            {isLoading ? 'Loading authentication...' : 'Checking authentication...'}
          </p>
        </div>
      </div>
    );
  }

  // Dashboard loading is handled separately and shouldn't block the main dashboard

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Enhanced Header */}
      <DashboardHeader />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enhanced Real-time Dashboard Summary */}
        <DashboardSummary />

        {/* Connection Status Indicator */}
        <div className="mb-6 flex items-center justify-between bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/20">
          <div className="flex items-center space-x-3">
            <div className={`h-3 w-3 rounded-full ${isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
            <span className="text-sm font-medium text-gray-700">
              {isConnected ? 'Real-time updates active' : 'Connecting...'}
            </span>
            {isConnected && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Live
              </span>
            )}
          </div>
          <div className="text-xs text-gray-500">
            Last updated: {dashboardData ? 'Just now' : 'Never'}
          </div>
        </div>

        {/* Quick Actions and Activity Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <QuickActions />
          <ActivityFeed />
        </div>

        {/* Analytics Grid - Comprehensive Dashboard */}
        <div className="space-y-4">
          {/* Primary Analytics Row */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <LoadAnalysis title="Load Analysis" />
            <BidAnalysis title="Bid Analysis" />
          </div>

          {/* Secondary Analytics Row */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <TransporterParticipationAnalysis title="Transporter Participation Analysis" />
            <TransporterAnalysis title="Transporter Analysis" />
          </div>

          {/* Tertiary Analytics Row */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <CancellationSurvey title="Cancellation Survey" />
            <BidPriceAnalysis title="Bid Price Analysis" />
          </div>

          {/* Quaternary Analytics Row */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <PerformanceAnalysis title="Performance Analysis" />
            <SalesTrend title="Sales Trend" />
          </div>
        </div>

        {/* Footer Section */}
        <div className="mt-12 bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Enhanced Logistics Management System
            </h3>
            <p className="text-gray-600 mb-4">
              Real-time analytics and comprehensive dashboard for efficient logistics operations
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-[#0e2d67] rounded-full"></div>
                <span>Primary Color: #0e2d67</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-[#e3000f] rounded-full"></div>
                <span>Secondary Color: #e3000f</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Real-time Updates</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
