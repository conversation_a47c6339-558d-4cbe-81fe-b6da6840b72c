export default function AdminDashboardPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Welcome to the Unified Transport Management System</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-blue-600 rounded"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Loads</p>
                <p className="text-2xl font-semibold text-gray-900">156</p>
                <p className="text-sm text-green-600">+12% from last month</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-red-600 rounded"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Live Loads</p>
                <p className="text-2xl font-semibold text-gray-900">23</p>
                <p className="text-sm text-green-600">Live bidding</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-blue-600 rounded"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Loads</p>
                <p className="text-2xl font-semibold text-gray-900">45</p>
                <p className="text-sm text-yellow-600">Awaiting bids</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-green-600 rounded"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Confirmed Loads</p>
                <p className="text-2xl font-semibold text-gray-900">89</p>
                <p className="text-sm text-green-600">Ready to ship</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Loads</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">Mumbai → Delhi</p>
                  <p className="text-sm text-gray-500">Load Date: July 5, 2025</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">₹50,000</p>
                  <p className="text-sm text-gray-500">5 quotations</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">Chennai → Bangalore</p>
                  <p className="text-sm text-gray-500">Load Date: July 6, 2025</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">₹30,000</p>
                  <p className="text-sm text-gray-500">3 quotations</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">Pune → Hyderabad</p>
                  <p className="text-sm text-gray-500">Load Date: July 4, 2025</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Completed
                  </span>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">₹40,000</p>
                  <p className="text-sm text-gray-500">7 quotations</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <button className="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition-colors">
            <div className="h-8 w-8 bg-white bg-opacity-20 rounded mb-2"></div>
            <h3 className="font-medium">Create New Load</h3>
            <p className="text-sm opacity-90 mt-1">Post a new load for bidding</p>
          </button>

          <button className="bg-red-600 text-white p-6 rounded-lg hover:bg-red-700 transition-colors">
            <div className="h-8 w-8 bg-white bg-opacity-20 rounded mb-2"></div>
            <h3 className="font-medium">Add Enterprise</h3>
            <p className="text-sm opacity-90 mt-1">Register a new enterprise</p>
          </button>

          <button className="bg-gray-600 text-white p-6 rounded-lg hover:bg-gray-700 transition-colors">
            <div className="h-8 w-8 bg-white bg-opacity-20 rounded mb-2"></div>
            <h3 className="font-medium">View Reports</h3>
            <p className="text-sm opacity-90 mt-1">Generate system reports</p>
          </button>
        </div>
      </div>
    </div>
  );
}
