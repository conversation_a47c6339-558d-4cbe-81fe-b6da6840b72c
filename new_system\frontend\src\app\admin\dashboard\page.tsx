'use client';

import { useEffect, useState } from 'react';
import {
  TruckIcon,
  BuildingOfficeIcon,
  UsersIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowPathIcon,
  ClockIcon,
  CogIcon,
  BellIcon,
  CalendarIcon,
  MapPinIcon,
  CurrencyRupeeIcon
} from '@heroicons/react/24/outline';

// Enhanced universal admin dashboard component matching bidding_system exactly
function AdminDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [error, setError] = useState(null);
  const [loadAnalysis, setLoadAnalysis] = useState(null);
  const [bidAnalysis, setBidAnalysis] = useState(null);
  const [transpoterParticipation, setTranspoterParticipation] = useState(null);

  // Real-time data fetching functions matching bidding_system
  const loadAnalysisData = async (response) => {
    try {
      const res = await fetch('/api/admin/dashboardLoadAnalysisChart', {
        method: 'POST',
        headers: {
          'Authorization': Bearer ,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ response })
      });
      if (res.ok) {
        const data = await res.json();
        setLoadAnalysis(data);
      }
    } catch (err) {
      console.error('Load analysis error:', err);
    }
  };

  const bidAnalysisData = async (response) => {
    try {
      const res = await fetch('/api/admin/dashboardBidAnalysis', {
        method: 'POST',
        headers: {
          'Authorization': Bearer ,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ response })
      });
      if (res.ok) {
        const data = await res.json();
        setBidAnalysis(data);
        // Update DOM elements like bidding_system
        const bidCntElement = document.getElementById('bidCnt');
        const avgBidElement = document.getElementById('avgBid');
        if (bidCntElement) bidCntElement.innerHTML = data.loadCount;
        if (avgBidElement) avgBidElement.innerHTML = data.avgBid;
      }
    } catch (err) {
      console.error('Bid analysis error:', err);
    }
  };

  const transpoterParticipationData = async (response) => {
    try {
      const res = await fetch('/api/admin/dashboardTranspoterParticipation', {
        method: 'POST',
        headers: {
          'Authorization': Bearer ,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ response })
      });
      if (res.ok) {
        const data = await res.json();
        setTranspoterParticipation(data);
      }
    } catch (err) {
      console.error('Transporter participation error:', err);
    }
  };

  useEffect(() => {
    // Fetch comprehensive dashboard data matching bidding_system
    const fetchDashboardData = async () => {
      try {
        const dashboardResponse = await fetch('/api/admin/dashboard', {
          headers: {
            'Authorization': Bearer ,
            'Content-Type': 'application/json'
          }
        });
        
        if (dashboardResponse.ok) {
          const dashboardData = await dashboardResponse.json();
          setDashboardData(dashboardData.data);
        }
        
        // Load initial analysis data
        await loadAnalysisData('search');
        await bidAnalysisData('search');
        await transpoterParticipationData('search');
        
      } catch (err) {
        setError('Network error occurred');
        console.error('Dashboard fetch error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();

    // Set up real-time updates every 10 minutes like bidding_system
    const interval = setInterval(() => {
      loadAnalysisData('search');
      bidAnalysisData('search');
      transpoterParticipationData('search');
    }, 600000); // 10 minutes

    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-[#0e2d67] mx-auto mb-6"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Admin Dashboard</h2>
          <p className="text-gray-600">Fetching real-time data from the system...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-6xl mb-6"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Dashboard Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="inline-flex items-center px-6 py-3 bg-[#0e2d67] text-white rounded-lg hover:bg-[#0e2d67]/90 transition-colors"
          >
            <ArrowPathIcon className="h-5 w-5 mr-2" />
            Retry Loading
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
              <p className="text-lg text-gray-600">
                Real-time insights and management for your transport operations
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors">
                <BellIcon className="h-6 w-6" />
                {dashboardData?.notify?.length > 0 && (
                  <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                )}
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-[#0e2d67] text-white rounded-lg hover:bg-[#0e2d67]/90 transition-colors">
                <CogIcon className="h-5 w-5 mr-2" />
                Settings
              </button>
            </div>
          </div>
        </div>

        {/* Key Metrics Grid - Matches bidding_system exactly */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <TruckIcon className="h-8 w-8 text-blue-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Loads</p>
                <p className="text-3xl font-bold text-gray-900">{dashboardData?.totalLoad || 0}</p>
                <div className="flex items-center mt-2">
                  <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600 font-medium">
                    +{dashboardData?.growthPercentage || 0}%
                  </span>
                  <span className="text-sm text-gray-500 ml-1">from last month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-red-100 rounded-lg">
                  <DocumentTextIcon className="h-8 w-8 text-red-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Live Loads</p>
                <p className="text-3xl font-bold text-gray-900">{dashboardData?.liveLoad || 0}</p>
                <div className="flex items-center mt-2">
                  <div className="h-2 w-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-sm text-green-600 font-medium">Active bidding</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <ClockIcon className="h-8 w-8 text-yellow-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Pending Loads</p>
                <p className="text-3xl font-bold text-gray-900">{dashboardData?.pendingLoad || 0}</p>
                <div className="flex items-center mt-2">
                  <ClockIcon className="h-4 w-4 text-yellow-500 mr-1" />
                  <span className="text-sm text-yellow-600 font-medium">Awaiting bids</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-green-100 rounded-lg">
                  <UsersIcon className="h-8 w-8 text-green-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Confirmed Loads</p>
                <p className="text-3xl font-bold text-gray-900">{dashboardData?.confirmedLoad || 0}</p>
                <div className="flex items-center mt-2">
                  <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600 font-medium">Ready to ship</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Section - Matches bidding_system exactly */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Load Analysis */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Load Analysis</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => loadAnalysisData('search')}
                  className="px-3 py-1 bg-[#0e2d67] text-white rounded text-sm hover:bg-[#0e2d67]/90"
                >
                  Refresh
                </button>
                <ChartBarIcon className="h-6 w-6 text-gray-400" />
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Loads</p>
                  <p className="text-2xl font-bold text-gray-900">{loadAnalysis?.total || dashboardData?.totalLoad || 0}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">This Period</p>
                  <div className="flex items-center">
                    <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-sm font-medium text-green-600">+{dashboardData?.growthPercentage || 0}%</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <p className="text-lg font-bold text-blue-600">{loadAnalysis?.pending || dashboardData?.pendingLoad || 0}</p>
                  <p className="text-xs text-blue-500">Pending</p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <p className="text-lg font-bold text-green-600">{loadAnalysis?.confirm || dashboardData?.confirmedLoad || 0}</p>
                  <p className="text-xs text-green-500">Confirmed</p>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <p className="text-lg font-bold text-red-600">{loadAnalysis?.cancel || 0}</p>
                  <p className="text-xs text-red-500">Cancelled</p>
                </div>
              </div>
            </div>
          </div>

          {/* Bid Analysis */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Bid Analysis</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => bidAnalysisData('search')}
                  className="px-3 py-1 bg-[#e3000f] text-white rounded text-sm hover:bg-[#e3000f]/90"
                >
                  Refresh
                </button>
                <CurrencyRupeeIcon className="h-6 w-6 text-gray-400" />
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Bids per Load</p>
                  <p className="text-2xl font-bold text-gray-900" id="avgBid">{bidAnalysis?.avgBid || dashboardData?.avgBid || 0}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Load Count</p>
                  <p className="text-lg font-semibold text-gray-700" id="bidCnt">{bidAnalysis?.loadCount || dashboardData?.bidLoadCount || 0}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <p className="text-lg font-bold text-purple-600">₹{dashboardData?.completeLoad || 0}</p>
                  <p className="text-xs text-purple-500">Completed Value</p>
                </div>
                <div className="text-center p-3 bg-indigo-50 rounded-lg">
                  <p className="text-lg font-bold text-indigo-600">{dashboardData?.quoteCount || 0}</p>
                  <p className="text-xs text-indigo-500">Total Quotes</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity & Notifications - Matches bidding_system */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Recent Loads */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">Recent Load Activity</h3>
                <button className="text-sm text-[#0e2d67] hover:text-[#0e2d67]/80 font-medium">
                  View All
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {dashboardData?.cancelLoad?.slice(0, 4).map((load, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <MapPinIcon className="h-8 w-8 text-gray-400" />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">{load.from_location} → {load.to_location}</p>
                        <div className="flex items-center space-x-4 mt-1">
                          <p className="text-sm text-gray-500 flex items-center">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            {new Date(load.created_at).toLocaleDateString()}
                          </p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            load.status === 'PUBLISHED' ? 'bg-green-100 text-green-800' :
                            load.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                            load.status === 'CONFIRMED' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {load.status}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">₹{load.budget || 'N/A'}</p>
                      <p className="text-sm text-gray-500">{load.quotation_count || 0} quotations</p>
                    </div>
                  </div>
                )) || [
                  <div key="no-data" className="text-center py-8 text-gray-500">
                    <TruckIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No recent load activity</p>
                  </div>
                ]}
              </div>
            </div>
          </div>

          {/* Notifications */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900">Notifications</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {dashboardData?.notify?.slice(0, 4).map((notification, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                    <BellIcon className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{notification.message}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(notification.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )) || [
                  <div key="default" className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                    <BellIcon className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">System is running smoothly</p>
                      <p className="text-xs text-gray-500 mt-1">No new notifications</p>
                    </div>
                  </div>
                ]}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <button className="group bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white p-6 rounded-xl hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg">
            <div className="flex items-center justify-center w-12 h-12 bg-white bg-opacity-20 rounded-lg mb-4 group-hover:bg-opacity-30 transition-all">
              <TruckIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Create New Load</h3>
            <p className="text-sm opacity-90">Post a new load for competitive bidding</p>
          </button>

          <button className="group bg-gradient-to-r from-[#e3000f] to-[#dc2626] text-white p-6 rounded-xl hover:from-[#dc2626] hover:to-[#e3000f] transition-all duration-300 transform hover:scale-105 shadow-lg">
            <div className="flex items-center justify-center w-12 h-12 bg-white bg-opacity-20 rounded-lg mb-4 group-hover:bg-opacity-30 transition-all">
              <BuildingOfficeIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Add Enterprise</h3>
            <p className="text-sm opacity-90">Register a new enterprise in the system</p>
          </button>

          <button className="group bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-xl hover:from-green-700 hover:to-green-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
            <div className="flex items-center justify-center w-12 h-12 bg-white bg-opacity-20 rounded-lg mb-4 group-hover:bg-opacity-30 transition-all">
              <UsersIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Manage Users</h3>
            <p className="text-sm opacity-90">Add, edit, or deactivate user accounts</p>
          </button>

          <button className="group bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6 rounded-xl hover:from-purple-700 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
            <div className="flex items-center justify-center w-12 h-12 bg-white bg-opacity-20 rounded-lg mb-4 group-hover:bg-opacity-30 transition-all">
              <ChartBarIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold mb-2">View Reports</h3>
            <p className="text-sm opacity-90">Generate comprehensive system reports</p>
          </button>
        </div>

        {/* Footer Stats - Matches bidding_system data structure */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{dashboardData?.transpoterDtls?.length || 0}</p>
              <p className="text-sm text-gray-500">Active Transporters</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{dashboardData?.consigneeDtls?.length || 0}</p>
              <p className="text-sm text-gray-500">Registered Consignees</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{dashboardData?.branchDtls?.length || 0}</p>
              <p className="text-sm text-gray-500">Branch Offices</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{dashboardData?.quoteCount || 0}</p>
              <p className="text-sm text-gray-500">Total Quotations</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AdminDashboardPage() {
  return <AdminDashboard />;
}
