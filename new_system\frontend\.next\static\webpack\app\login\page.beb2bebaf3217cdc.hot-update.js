"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LoginPage() {\n    var _localStorage_getItem;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { smsOtpLogin } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // 1: mobile, 2: otp, 3: success\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        login_mobile: \"\",\n        user_otp: \"\",\n        user_id: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Step 1: Send OTP\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/auth/bypass/get-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    login_mobile: formData.login_mobile\n                })\n            });\n            const data = await response.json();\n            if (data.alert === \"success\") {\n                setFormData((prev)=>({\n                        ...prev,\n                        user_id: data.user_id\n                    }));\n                setStep(2);\n            } else {\n                setError(data.message || \"Failed to send OTP\");\n            }\n        } catch (err) {\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Step 2: Verify OTP\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"Sending OTP verification request:\", {\n                user_id: formData.user_id,\n                user_otp: formData.user_otp\n            });\n            const response = await fetch(\"/api/auth/bypass/login-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    user_id: formData.user_id,\n                    user_otp: formData.user_otp\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            const data = await response.json();\n            console.log(\"Response data:\", data);\n            console.log(\"=== AUTHENTICATION RESPONSE ===\");\n            console.log(\"Full response data:\", data);\n            console.log(\"Alert status:\", data.alert);\n            console.log(\"Redirect URL:\", data.redirectUrl);\n            console.log(\"================================\");\n            if (data.alert === \"success\") {\n                console.log(\"✅ Authentication successful!\");\n                // Store all data immediately\n                localStorage.setItem(\"token\", data.token);\n                localStorage.setItem(\"user\", JSON.stringify(data.user));\n                localStorage.setItem(\"isAuthenticated\", \"true\");\n                localStorage.setItem(\"tempUser\", JSON.stringify(data.user));\n                localStorage.setItem(\"pendingRedirectUrl\", data.redirectUrl || \"/dashboard\");\n                // Show success step with immediate redirect options\n                setStep(3);\n                // Try multiple redirect methods\n                console.log(\"\\uD83D\\uDD04 Attempting multiple redirect methods...\");\n                // Method 1: Immediate redirect\n                try {\n                    window.location.href = data.redirectUrl || \"/dashboard\";\n                } catch (e) {\n                    console.log(\"Method 1 failed:\", e);\n                }\n                // Method 2: Replace location\n                setTimeout(()=>{\n                    try {\n                        window.location.replace(data.redirectUrl || \"/dashboard\");\n                    } catch (e) {\n                        console.log(\"Method 2 failed:\", e);\n                    }\n                }, 500);\n                // Method 3: Assign location\n                setTimeout(()=>{\n                    try {\n                        window.location.assign(data.redirectUrl || \"/dashboard\");\n                    } catch (e) {\n                        console.log(\"Method 3 failed:\", e);\n                    }\n                }, 1000);\n            } else {\n                console.log(\"❌ Authentication failed:\", data);\n                setError(data.message || \"Invalid OTP\");\n            }\n        } catch (err) {\n            console.error(\"Network error during OTP verification:\", err);\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#0e2d67] via-[#1e3a8a] to-[#e3000f] flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-16 h-16 bg-gradient-to-r from-[#0e2d67] to-[#e3000f] rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"eParivahan Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: step === 1 ? \"Enter your mobile number\" : step === 2 ? \"Enter the OTP sent to your mobile\" : \"Login successful! Redirecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this),\n                step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3\",\n                                children: \"✅ Login successful!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2 text-sm\",\n                                children: \"If automatic redirect doesn't work, use buttons below:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log(\"Manual redirect to auth-success clicked\");\n                                            const redirectUrl = localStorage.getItem(\"pendingRedirectUrl\") || \"/auth-success\";\n                                            console.log(\"Redirecting to:\", redirectUrl);\n                                            window.location.href = redirectUrl;\n                                        },\n                                        className: \"block w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm\",\n                                        children: \"\\uD83D\\uDD04 Go to Auth Success Page\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log(\"Manual redirect to dashboard clicked\");\n                                            window.location.href = \"/dashboard\";\n                                        },\n                                        className: \"block w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                                        children: \"\\uD83D\\uDCCA Go to Dashboard Directly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log(\"Force reload and redirect\");\n                                            localStorage.setItem(\"forceRedirect\", \"true\");\n                                            window.location.reload();\n                                        },\n                                        className: \"block w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm\",\n                                        children: \"\\uD83D\\uDD04 Force Reload & Redirect\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 text-xs text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Token: \",\n                                            (_localStorage_getItem = localStorage.getItem(\"token\")) === null || _localStorage_getItem === void 0 ? void 0 : _localStorage_getItem.substring(0, 20),\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"User: \",\n                                            JSON.parse(localStorage.getItem(\"user\") || \"{}\").name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Redirect URL: \",\n                                            localStorage.getItem(\"pendingRedirectUrl\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false),\n                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Mobile Number \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 31\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: formData.login_mobile,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        login_mobile: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                            placeholder: \"Enter 10-digit mobile number\",\n                                            pattern: \"[6-9][0-9]{9}\",\n                                            maxLength: \"10\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: \"Enter mobile number starting with 6, 7, 8, or 9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading || formData.login_mobile.length !== 10,\n                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Sending OTP...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this) : \"Send OTP\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this),\n                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleVerifyOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Enter OTP \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.user_otp,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        user_otp: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent text-center text-lg tracking-widest\",\n                                            placeholder: \"000000\",\n                                            maxLength: \"6\",\n                                            pattern: \"[0-9]{6}\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: [\n                                        \"OTP sent to +91 \",\n                                        formData.login_mobile\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setStep(1),\n                                    className: \"flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg hover:bg-gray-200 transition-colors\",\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || formData.user_otp.length !== 6,\n                                    className: \"flex-1 bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Verifying...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Login\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 eParivahan. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"LVXK5e1QL+GynZVgxj92VNUDRVI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ })

});