"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,KeyIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { smsOtpLogin } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // 1: mobile, 2: otp, 3: success\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        login_mobile: \"\",\n        user_otp: \"\",\n        user_id: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Step 1: Send OTP\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/auth/bypass/get-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    login_mobile: formData.login_mobile\n                })\n            });\n            const data = await response.json();\n            if (data.alert === \"success\") {\n                setFormData((prev)=>({\n                        ...prev,\n                        user_id: data.user_id\n                    }));\n                setStep(2);\n            } else {\n                setError(data.message || \"Failed to send OTP\");\n            }\n        } catch (err) {\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Step 2: Verify OTP\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"Sending OTP verification request:\", {\n                user_id: formData.user_id,\n                user_otp: formData.user_otp\n            });\n            const response = await fetch(\"/api/auth/bypass/login-otp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    user_id: formData.user_id,\n                    user_otp: formData.user_otp\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            const data = await response.json();\n            console.log(\"Response data:\", data);\n            if (data.alert === \"success\") {\n                console.log(\"Authentication successful, response data:\", data);\n                // Show success step first\n                setStep(3);\n                try {\n                    // Use auth store to properly set authentication state\n                    console.log(\"Calling smsOtpLogin with:\", {\n                        user: data.user,\n                        token: data.token\n                    });\n                    smsOtpLogin(data.user, data.token, data.token); // Using same token for both access and refresh\n                    console.log(\"Auth store updated successfully\");\n                    console.log(\"User type received:\", data.user_type);\n                    // Add a small delay to ensure auth store is updated\n                    setTimeout(()=>{\n                        // Redirect based on user type - using window.location for more reliable redirection\n                        let redirectUrl = \"/dashboard\"; // default\n                        if (data.user_type === \"admin/dashboard\") {\n                            console.log(\"Redirecting to admin dashboard\");\n                            redirectUrl = \"/admin/dashboard\";\n                        } else if (data.user_type === \"consignee/dashboard\") {\n                            console.log(\"Redirecting to consignee dashboard\");\n                            redirectUrl = \"/consignee/dashboard\";\n                        } else if (data.user_type === \"supply-partner/dashboard\") {\n                            console.log(\"Redirecting to supply-partner dashboard\");\n                            redirectUrl = \"/supply-partner/dashboard\";\n                        } else {\n                            console.log(\"Redirecting to default dashboard\");\n                            redirectUrl = \"/dashboard\";\n                        }\n                        console.log(\"Final redirect URL:\", redirectUrl);\n                        // Try both methods for maximum compatibility\n                        try {\n                            router.push(redirectUrl);\n                            console.log(\"Router.push called successfully\");\n                        } catch (routerError) {\n                            console.error(\"Router.push failed, using window.location:\", routerError);\n                            window.location.href = redirectUrl;\n                        }\n                    }, 2000); // Increased delay to 2 seconds to see the success message\n                } catch (authError) {\n                    console.error(\"Auth store error:\", authError);\n                    setError(\"Authentication setup failed. Please try again.\");\n                }\n            } else {\n                console.log(\"Authentication failed:\", data);\n                setError(data.message || \"Invalid OTP\");\n            }\n        } catch (err) {\n            console.error(\"Network error during OTP verification:\", err);\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#0e2d67] via-[#1e3a8a] to-[#e3000f] flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-16 h-16 bg-gradient-to-r from-[#0e2d67] to-[#e3000f] rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"eParivahan Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: step === 1 ? \"Enter your mobile number\" : \"Enter the OTP sent to your mobile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this),\n                step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-3\",\n                            children: \"✅ Login successful! Redirecting to dashboard...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                console.log(\"Manual redirect button clicked\");\n                                window.location.href = \"/dashboard\";\n                            },\n                            className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                            children: \"Go to Dashboard Manually\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, this),\n                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Mobile Number \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 31\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: formData.login_mobile,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        login_mobile: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent\",\n                                            placeholder: \"Enter 10-digit mobile number\",\n                                            pattern: \"[6-9][0-9]{9}\",\n                                            maxLength: \"10\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: \"Enter mobile number starting with 6, 7, 8, or 9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading || formData.login_mobile.length !== 10,\n                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Sending OTP...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 17\n                            }, this) : \"Send OTP\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, this),\n                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleVerifyOtp,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Enter OTP \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.user_otp,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        user_otp: e.target.value\n                                                    })),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0e2d67] focus:border-transparent text-center text-lg tracking-widest\",\n                                            placeholder: \"000000\",\n                                            maxLength: \"6\",\n                                            pattern: \"[0-9]{6}\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: [\n                                        \"OTP sent to +91 \",\n                                        formData.login_mobile\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setStep(1),\n                                    className: \"flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg hover:bg-gray-200 transition-colors\",\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || formData.user_otp.length !== 6,\n                                    className: \"flex-1 bg-gradient-to-r from-[#0e2d67] to-[#1e3a8a] text-white py-3 rounded-lg hover:from-[#1e3a8a] hover:to-[#0e2d67] transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Verifying...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_KeyIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Login\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 eParivahan. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"LVXK5e1QL+GynZVgxj92VNUDRVI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ })

});