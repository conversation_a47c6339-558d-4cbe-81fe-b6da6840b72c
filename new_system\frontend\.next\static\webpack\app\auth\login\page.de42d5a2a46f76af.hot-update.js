"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// SMS OTP Login Schema (Mobile-only login as per legacy system)\nconst mobileLoginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    mobile: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().regex(/^[6-9]\\d{9}$/, \"Mobile number must be 10 digits starting with 6-9\").length(10, \"Mobile number must be exactly 10 digits\")\n});\n// OTP Verification Schema\nconst otpVerificationSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    otp: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().length(6, \"OTP must be exactly 6 digits\").regex(/^\\d{6}$/, \"OTP must contain only numbers\")\n});\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoading } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    const [mobile, setMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [otpSent, setOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Mobile login form\n    const mobileForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(mobileLoginSchema)\n    });\n    // OTP verification form\n    const otpForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(otpVerificationSchema)\n    });\n    // Send OTP\n    const onMobileSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/get-otp\", {\n                login_mobile: data.mobile\n            });\n            if (response.data.alert === \"success\") {\n                setMobile(data.mobile);\n                setUserId(response.data.user_id);\n                setStep(\"otp\");\n                setOtpSent(true);\n                setCountdown(120); // 2 minutes countdown\n                // Start countdown timer\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP sent to your mobile number!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.message || \"Failed to send OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to send OTP\");\n        }\n    };\n    // Verify OTP\n    const onOtpSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/login-otp\", {\n                user_id: userId,\n                user_otp: data.otp\n            });\n            if (response.data.success) {\n                const { tokens, user } = response.data.data;\n                // Update auth store state (this will also handle localStorage/cookies)\n                const authStore = _store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore.getState();\n                authStore.smsOtpLogin(user, tokens.accessToken, tokens.refreshToken);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Login successful!\");\n                // Redirect to main dashboard (all user types use the same dashboard)\n                router.push(\"/dashboard\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.error || \"Invalid OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"OTP verification failed\");\n        }\n    };\n    // Resend OTP\n    const resendOtp = async ()=>{\n        if (countdown > 0) return;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/get-otp\", {\n                login_mobile: mobile\n            });\n            if (response.data.success) {\n                setCountdown(120);\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP resent successfully!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to resend OTP\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                        backgroundSize: \"60px 60px\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 w-16 rounded-2xl bg-gradient-to-br from-[#0e2d67] to-[#1a4480] flex items-center justify-center shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-[#e3000f] flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-xs\",\n                                        children: \"EP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: step === \"mobile\" ? \"Welcome to eParivahan\" : \"Verify OTP\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: step === \"mobile\" ? \"Enter your mobile number to receive OTP\" : \"OTP sent to \".concat(mobile, \". Enter the 6-digit code.\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8\",\n                    children: [\n                        step === \"mobile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: mobileForm.handleSubmit(onMobileSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"mobile\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Mobile Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...mobileForm.register(\"mobile\"),\n                                                    type: \"tel\",\n                                                    autoComplete: \"tel\",\n                                                    maxLength: 10,\n                                                    className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(mobileForm.formState.errors.mobile ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-lg tracking-wider\"),\n                                                    placeholder: \"9876543210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"+91\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        mobileForm.formState.errors.mobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this),\n                                                mobileForm.formState.errors.mobile.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Sending OTP...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Send OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this),\n                        step === \"otp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: otpForm.handleSubmit(onOtpSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"otp\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Enter OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...otpForm.register(\"otp\"),\n                                                type: \"text\",\n                                                maxLength: 6,\n                                                className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(otpForm.formState.errors.otp ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-center text-2xl tracking-widest font-mono\"),\n                                                placeholder: \"000000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        otpForm.formState.errors.otp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this),\n                                                otpForm.formState.errors.otp.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this),\n                                countdown > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Resend OTP in \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-[#0e2d67]\",\n                                                children: [\n                                                    Math.floor(countdown / 60),\n                                                    \":\",\n                                                    (countdown % 60).toString().padStart(2, \"0\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verifying...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verify & Login\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: resendOtp,\n                                            disabled: countdown > 0,\n                                            className: \"w-full border-2 border-gray-200 text-gray-700 font-semibold py-2 px-4 rounded-xl hover:border-[#0e2d67] hover:text-[#0e2d67] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 bg-white/50 backdrop-blur-sm\",\n                                            children: countdown > 0 ? \"Please wait...\" : \"Resend OTP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                setStep(\"mobile\");\n                                                setCountdown(0);\n                                                otpForm.reset();\n                                            },\n                                            className: \"w-full text-gray-600 font-medium py-2 px-4 rounded-xl hover:text-[#0e2d67] focus:outline-none transition-all duration-200\",\n                                            children: \"← Change Mobile Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-100 rounded-2xl p-6 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg bg-blue-500 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"\\uD83D\\uDD11\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-bold text-blue-900\",\n                                    children: \"Demo Login Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Admin User\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8240301895\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Consignee (Load Poster)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9874896900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8181816477\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9051720133\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs font-semibold text-blue-800 mb-2\",\n                                    children: \"Default OTPs (Admin Access)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-2\",\n                                    children: [\n                                        \"123456\",\n                                        \"111111\",\n                                        \"000000\",\n                                        \"999999\",\n                                        \"555555\"\n                                    ].map((otp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-2 text-center border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs font-mono text-blue-900\",\n                                                children: otp\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, otp, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-2\",\n                                    children: \"These OTPs work for any mobile number for testing purposes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"ggp3cQO+eSU+p6y29aLI5bJzFZc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ })

});