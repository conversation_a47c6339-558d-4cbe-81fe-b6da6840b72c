"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DevicePhoneMobileIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// SMS OTP Login Schema (Mobile-only login as per legacy system)\nconst mobileLoginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    mobile: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().regex(/^[6-9]\\d{9}$/, \"Mobile number must be 10 digits starting with 6-9\").length(10, \"Mobile number must be exactly 10 digits\")\n});\n// OTP Verification Schema\nconst otpVerificationSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    otp: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().length(6, \"OTP must be exactly 6 digits\").regex(/^\\d{6}$/, \"OTP must contain only numbers\")\n});\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoading } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    const [mobile, setMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [otpSent, setOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Mobile login form\n    const mobileForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(mobileLoginSchema)\n    });\n    // OTP verification form\n    const otpForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(otpVerificationSchema)\n    });\n    // Send OTP\n    const onMobileSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/get-otp\", {\n                login_mobile: data.mobile\n            });\n            if (response.data.alert === \"success\") {\n                setMobile(data.mobile);\n                setUserId(response.data.user_id);\n                setStep(\"otp\");\n                setOtpSent(true);\n                setCountdown(120); // 2 minutes countdown\n                // Start countdown timer\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP sent to your mobile number!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.message || \"Failed to send OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to send OTP\");\n        }\n    };\n    // Verify OTP\n    const onOtpSubmit = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/login-otp\", {\n                user_id: userId,\n                user_otp: data.otp\n            });\n            if (response.data.alert === \"success\") {\n                const { token, user } = response.data;\n                // Store token and user data\n                localStorage.setItem(\"token\", token);\n                localStorage.setItem(\"user\", JSON.stringify(user));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Login successful!\");\n                // Redirect based on user type - matches bidding_system exactly\n                if (response.data.user_type === \"admin/dashboard\") {\n                    router.push(\"/admin/dashboard\");\n                } else if (response.data.user_type === \"consignee/dashboard\") {\n                    router.push(\"/consignee/dashboard\");\n                } else if (response.data.user_type === \"supply-partner/dashboard\") {\n                    router.push(\"/supply-partner/dashboard\");\n                } else {\n                    router.push(\"/dashboard\");\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.data.message || \"Invalid OTP\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"OTP verification failed\");\n        }\n    };\n    // Resend OTP\n    const resendOtp = async ()=>{\n        if (countdown > 0) return;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.post(\"/auth/bypass/get-otp\", {\n                login_mobile: mobile\n            });\n            if (response.data.alert === \"success\") {\n                setCountdown(120);\n                const timer = setInterval(()=>{\n                    setCountdown((prev)=>{\n                        if (prev <= 1) {\n                            clearInterval(timer);\n                            return 0;\n                        }\n                        return prev - 1;\n                    });\n                }, 1000);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"OTP resent successfully!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to resend OTP\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                        backgroundSize: \"60px 60px\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 w-16 rounded-2xl bg-gradient-to-br from-[#0e2d67] to-[#1a4480] flex items-center justify-center shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-[#e3000f] flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-xs\",\n                                        children: \"EP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: step === \"mobile\" ? \"Welcome to eParivahan\" : \"Verify OTP\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: step === \"mobile\" ? \"Enter your mobile number to receive OTP\" : \"OTP sent to \".concat(mobile, \". Enter the 6-digit code.\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8\",\n                    children: [\n                        step === \"mobile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: mobileForm.handleSubmit(onMobileSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"mobile\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Mobile Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...mobileForm.register(\"mobile\"),\n                                                    type: \"tel\",\n                                                    autoComplete: \"tel\",\n                                                    maxLength: 10,\n                                                    className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(mobileForm.formState.errors.mobile ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-lg tracking-wider\"),\n                                                    placeholder: \"9876543210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"+91\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        mobileForm.formState.errors.mobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this),\n                                                mobileForm.formState.errors.mobile.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Sending OTP...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Send OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        step === \"otp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: otpForm.handleSubmit(onOtpSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"otp\",\n                                            className: \"block text-sm font-semibold text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Enter OTP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...otpForm.register(\"otp\"),\n                                                type: \"text\",\n                                                maxLength: 6,\n                                                className: \"w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-0 \".concat(otpForm.formState.errors.otp ? \"border-red-300 focus:border-red-500\" : \"border-gray-200 focus:border-[#0e2d67] hover:border-gray-300\", \" bg-white/50 backdrop-blur-sm text-center text-2xl tracking-widest font-mono\"),\n                                                placeholder: \"000000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        otpForm.formState.errors.otp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: \"⚠\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this),\n                                                otpForm.formState.errors.otp.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                countdown > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Resend OTP in \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-[#0e2d67]\",\n                                                children: [\n                                                    Math.floor(countdown / 60),\n                                                    \":\",\n                                                    (countdown % 60).toString().padStart(2, \"0\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            className: \"w-full bg-gradient-to-r from-[#0e2d67] to-[#1a4480] text-white font-semibold py-3 px-4 rounded-xl hover:from-[#1a4480] hover:to-[#2659a3] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verifying...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DevicePhoneMobileIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Verify & Login\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: resendOtp,\n                                            disabled: countdown > 0,\n                                            className: \"w-full border-2 border-gray-200 text-gray-700 font-semibold py-2 px-4 rounded-xl hover:border-[#0e2d67] hover:text-[#0e2d67] focus:outline-none focus:ring-4 focus:ring-[#0e2d67]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 bg-white/50 backdrop-blur-sm\",\n                                            children: countdown > 0 ? \"Please wait...\" : \"Resend OTP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                setStep(\"mobile\");\n                                                setCountdown(0);\n                                                otpForm.reset();\n                                            },\n                                            className: \"w-full text-gray-600 font-medium py-2 px-4 rounded-xl hover:text-[#0e2d67] focus:outline-none transition-all duration-200\",\n                                            children: \"← Change Mobile Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-100 rounded-2xl p-6 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg bg-blue-500 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"\\uD83D\\uDD11\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-bold text-blue-900\",\n                                    children: \"Demo Login Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Admin User\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8240301895\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Consignee (Load Poster)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9874896900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 8181816477\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/60 rounded-lg p-3 border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-semibold text-blue-800 mb-1\",\n                                            children: \"Fleet Owner (Transporter)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-900 font-mono\",\n                                            children: \"\\uD83D\\uDCF1 9051720133\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs font-semibold text-blue-800 mb-2\",\n                                    children: \"Default OTPs (Admin Access)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-2\",\n                                    children: [\n                                        \"123456\",\n                                        \"111111\",\n                                        \"000000\",\n                                        \"999999\",\n                                        \"555555\"\n                                    ].map((otp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-2 text-center border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs font-mono text-blue-900\",\n                                                children: otp\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, otp, false, {\n                                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-2\",\n                                    children: \"These OTPs work for any mobile number for testing purposes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\eparivahan\\\\augment_june_26_26\\\\new_system\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"ggp3cQO+eSU+p6y29aLI5bJzFZc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_authStore__WEBPACK_IMPORTED_MODULE_6__.useAuthStore,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ })

});